# B2C Frontend

B2C Frontend is a React Native application designed as a networking platform for mariners

## Setup & Installation

1. Clone the repository:
   ```sh
   git clone https://github.com/navicater/b2c-frontend.git
   cd b2c-frontend
   ```
2. Install dependencies:
   ```sh
   npm install
   ```
3. Create a `.env` file. (Refer `.env.example`)
4. Start the metro bundler in a terminal
   ```sh
   npm run start
   ```
5. Start the application:

   i. For ios

   ```sh
      cd ios
      pod install
      cd ..
      npm run ios
   ```

   ii. For android

   ```sh
   npm run android
   ```

Contribute!
