/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

declare module 'react-native-config' {
  interface NativeConfig {
    API_KEY: string;
    AI_URL: string;
    BASE_URL: string;
    ENV: string;
    FIREBASE_ANDROID_APP_ID: string;
    FIREBASE_API_KEY: string;
    FIREBASE_AUTH_DOMAIN: string;
    FIREBASE_DATABASE_URL?: string;
    FIREBASE_IOS_APP_ID: string;
    FIREBASE_MESSAGING_SENDER_ID: string;
    FIREBASE_PROJECT_ID: string;
    FIREBASE_STORAGE_BUCKET: string;
    IOS_CLIENT_ID: string;
    SENTRY_DSN_URL: string;
    WEB_CLIENT_ID: string;
  }

  const Config: NativeConfig;
  export default Config;
}
