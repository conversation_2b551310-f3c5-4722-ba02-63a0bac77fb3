name: 'Upload to Google Play Console'
description: 'Uploads Android AAB bundle to Google Play Console'

inputs:
  package-name:
    description: 'Android package name'
    required: true
  track:
    description: 'Release track (internal, alpha, production)'
    required: true
    default: 'alpha'
  service-account-json:
    description: 'Google Play service account JSON'
    required: true
  aab-path:
    description: 'Path to AAB file'
    required: true
    default: 'android/app/build/outputs/bundle/release/app-release.aab'

runs:
  using: 'composite'
  steps:
    - name: Validate AAB File
      shell: bash
      run: |
        echo "📁 Validating AAB file"
        echo "Track: ${{ inputs.track }}"
        echo "Package Name: ${{ inputs.package-name }}"
        echo "AAB Path: ${{ inputs.aab-path }}"

        AAB_FILE="${{ inputs.aab-path }}"
        if [ ! -f "$AAB_FILE" ]; then
          echo "❌ Error: AAB file not found at $AAB_FILE"
          exit 1
        fi
        echo "✅ AAB file found!"

    - name: Upload to Google Play Console
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ inputs.service-account-json }}
        packageName: ${{ inputs.package-name }}
        releaseFiles: ${{ inputs.aab-path }}
        track: ${{ inputs.track }}
        status: completed
