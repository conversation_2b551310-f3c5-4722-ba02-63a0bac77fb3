/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export interface OnboardingPersonalBodyI {
  fullName?: string;
  countryIso2?: string;
  gender?: string;
}

export interface OnboardingPersonalResultI {
  isPersonalDetailsSaved: boolean;
  profileId: string;
}

export interface OnboardingWorkBodyI {
  designation: Pick<SearchResultI, 'id' | 'dataType'>;
  entity?: Pick<SearchResultI, 'id' | 'dataType'>;
}

export interface OnboardingWorkResultI {
  isWorkDetailsSaved: boolean;
  profileId: string;
}
