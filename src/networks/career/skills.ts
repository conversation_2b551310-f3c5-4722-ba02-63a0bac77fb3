import { apiCall } from '@/src/services/api';
import { DeleteSkillPayloadI, fetchSkillsResultI, SkillsPayloadI } from './types';

export const fetchSkillsAPI = async (profileId: string): Promise<fetchSkillsResultI[]> => {
  const maritimeSkills = await apiCall<string, fetchSkillsResultI[]>(
    '/backend/api/v1/career/profile-skills',
    'GET',
    {
      isAuth: true,
      query: {
        profileId,
        category: 'MARITIME',
      },
    },
  );

  const otherSkills = await apiCall<string, fetchSkillsResultI[]>(
    '/backend/api/v1/career/profile-skills',
    'GET',
    {
      isAuth: true,
      query: {
        profileId,
        category: 'OTHER',
      },
    },
  );

  return [...maritimeSkills, ...otherSkills];
};

export const addSkills = async (payload: SkillsPayloadI[]): Promise<number> => {
  const result = await apiCall<unknown, number>('/backend/api/v1/career/profile-skills', 'POST', {
    isAuth: true,
    payload,
  });

  return result;
};

export const deleteSkillsAPI = async (skills: DeleteSkillPayloadI[]) => {
  await apiCall<unknown>(`/backend/api/v1/career/profile-skills`, 'DELETE', {
    isAuth: true,
    payload: {
      skills,
    },
  });
};
