import { apiCall } from '@/src/services/api';
import { DeleteExperienceQueryI, ExperienceFetchParamsI, ExperienceFetchReponseI } from './types';

export const fetchAllExperiences = async (profileId: string) => {
  const response = await apiCall<ExperienceFetchParamsI, ExperienceFetchReponseI>(
    '/backend/api/v1/career/profile-experiences',
    'GET',
    {
      isAuth: true,
      query: { profileId },
    },
  );

  return response;
};

export const deleteExperience = async (payload: DeleteExperienceQueryI) => {
  await apiCall<DeleteExperienceQueryI, void>('/backend/api/v1/career/profile-experience', 'POST', {
    isAuth: true,
    payload,
  });
};

export const addExperience = async (payload: any) => {
  const response = await apiCall('/backend/api/v1/career/profile-experience', 'POST', {
    isAuth: true,
    payload,
  });

  return response;
};
