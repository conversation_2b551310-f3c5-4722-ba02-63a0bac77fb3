import { apiCall } from '@/src/services/api';
import {
  ScrapbookReactionListQueryI,
  ScrapbookReactionUpsertBodyI,
  ScrapbookReactionDeleteBodyI,
  ScrapbookPostCreateBodyI,
  ScrapbookPostListQueryI,
  ScrapBookPostFetchForClientIResponse,
  ScrapBookPostFetchForClientI,
  ScrapBookPostDeletePayloadI,
  ScrapBookCommentFetchManyQueryI,
  ScrapBookCommentFetchRepliesQueryI,
  ScrapBookCommentCreateBodyI,
  ScrapBookCommentFetchManyResultI,
  ScrapBookCommentCreateOneResultI,
  ScrapbookReactionResultI,
} from './types';

export const fetchScrapbookReactions = async (query: ScrapbookReactionListQueryI) => {
  const result = await apiCall<unknown, ScrapbookReactionResultI>(
    '/backend/api/v1/port/scrap-book/reactions',
    'GET',
    {
      query,
      isAuth: true,
    },
  );

  return result;
};

export const upsertScrapbookReaction = async (payload: ScrapbookReactionUpsertBodyI) => {
  const result = await apiCall('/backend/api/v1/port/scrap-book/reaction', 'POST', {
    payload,
    isAuth: true,
  });

  return result;
};

export const deleteScrapbookReaction = async (payload: ScrapbookReactionDeleteBodyI) => {
  const result = await apiCall('/backend/api/v1/port/scrap-book/reaction', 'DELETE', {
    payload,
    isAuth: true,
  });

  return result;
};

export const fetchScrapbookPosts = async (query: ScrapbookPostListQueryI) => {
  const result = await apiCall<unknown, ScrapBookPostFetchForClientIResponse>(
    '/backend/api/v1/port/scrap-book/posts',
    'GET',
    {
      query,
      isAuth: true,
    },
  );

  return result;
};

export const fetchScrapbookPost = async (routeId: string) => {
  const result = await apiCall<unknown, ScrapBookPostFetchForClientI>(
    '/backend/api/v1/port/scrap-book/post',
    'GET',
    {
      routeId,
      isAuth: true,
    },
  );

  return result;
};

export const editScrapbookPost = async (routeId: string, payload: ScrapbookPostCreateBodyI) => {
  const result = await apiCall<ScrapbookPostCreateBodyI, ScrapBookPostFetchForClientI>(
    `/backend/api/v1/port/scrap-book/post`,
    'PATCH',
    {
      payload,
      routeId,
      isAuth: true,
    },
  );

  return result;
};

export const createScrapbookPost = async (payload: ScrapbookPostCreateBodyI) => {
  const result = await apiCall<ScrapbookPostCreateBodyI, ScrapBookPostFetchForClientI>(
    '/backend/api/v1/port/scrap-book/post',
    'POST',
    {
      payload,
      isAuth: true,
    },
  );

  return result;
};

export const deleteScrapbookPost = async (payload: ScrapBookPostDeletePayloadI) => {
  const result = await apiCall('/backend/api/v1/port/scrap-book/post', 'DELETE', {
    isAuth: true,
    payload,
  });

  return result;
};

export const fetchScrapbookComments = async (query: ScrapBookCommentFetchManyQueryI) => {
  const result = await apiCall<unknown, ScrapBookCommentFetchManyResultI>(
    '/backend/api/v1/port/scrap-book/comments',
    'GET',
    {
      query,
      isAuth: true,
    },
  );

  return result;
};

export const fetchScrapbookCommentReplies = async (query: ScrapBookCommentFetchRepliesQueryI) => {
  const { parentCommentId, ...restQuery } = query;

  const result = await apiCall<unknown, ScrapBookCommentFetchManyResultI>(
    `/backend/api/v1/port/scrap-book/comment/${parentCommentId}/replies`,
    'GET',
    {
      query: restQuery,
      isAuth: true,
    },
  );

  return result;
};

export const createScrapbookComment = async (payload: ScrapBookCommentCreateBodyI) => {
  const result = await apiCall<ScrapBookCommentCreateBodyI, ScrapBookCommentCreateOneResultI>(
    '/backend/api/v1/port/scrap-book/comment',
    'POST',
    {
      payload,
      isAuth: true,
    },
  );

  return result;
};

export const deleteScrapbookComment = async (commentId: string) => {
  const result = await apiCall(`/backend/api/v1/port/scrap-book/comment`, 'DELETE', {
    isAuth: true,
    payload: {
      id: commentId,
    },
  });

  return result;
};
