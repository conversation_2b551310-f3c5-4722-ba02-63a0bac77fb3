import { useEffect, useRef, useState } from 'react';
import { MessageHand<PERSON>, SocketMessage } from './types';

export const useSocket = (baseUrl: string, profileId: string) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  const socketRef = useRef<WebSocket | null>(null);
  const messageHandlersRef = useRef<MessageHandler>({});
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const initializationRef = useRef(false);
  const connectionPromiseRef = useRef<Promise<void> | null>(null);

  const connect = () => {
    if (isConnected || isConnecting || socketRef.current?.readyState === WebSocket.OPEN) {
      return Promise.resolve();
    }

    if (socketRef.current?.readyState === WebSocket.CONNECTING) {
      return connectionPromiseRef.current || Promise.resolve();
    }

    if (connectionPromiseRef.current) {
      return connectionPromiseRef.current;
    }

    connectionPromiseRef.current = new Promise<void>((resolve, reject) => {
      try {
        setIsConnecting(true);
        const url = `ws://${baseUrl}/${profileId}`;
        socketRef.current = new WebSocket(url);
        const connectionTimeout = setTimeout(() => {
          setIsConnecting(false);
          connectionPromiseRef.current = null;
          if (socketRef.current && socketRef.current.readyState === WebSocket.CONNECTING) {
            socketRef.current.close();
            reject(new Error('Connection timeout'));
          }
        }, 10000);

        socketRef.current.onopen = () => {
          clearTimeout(connectionTimeout);
          setIsConnected(true);
          setIsConnecting(false);
          reconnectAttemptsRef.current = 0;
          connectionPromiseRef.current = null;
          resolve();
        };

        socketRef.current.onclose = (event) => {
          clearTimeout(connectionTimeout);
          setIsConnected(false);
          setIsConnecting(false);
          connectionPromiseRef.current = null;

          if (event.code !== 1000) {
            attemptReconnect();
          }
        };

        socketRef.current.onerror = (error) => {
          clearTimeout(connectionTimeout);
          setIsConnecting(false);
          connectionPromiseRef.current = null;
          if (!isConnected) {
            reject(error);
          }
        };

        socketRef.current.onmessage = (event) => {
          try {
            const message: SocketMessage = JSON.parse(event.data);
            const { type, data } = message;
            if (type === 'connection-established') {
              return;
            }
            if (type && messageHandlersRef.current[type]) {
              messageHandlersRef.current[type](data);
            }
          } catch (error) {
            console.error('[Socket] Error processing message:', error);
            console.error('[Socket] Raw message data:', event.data);
          }
        };
      } catch (error) {
        console.error('[Socket] Error creating WebSocket connection:', error);
        setIsConnecting(false);
        connectionPromiseRef.current = null;
        reject(error);
      }
    });

    return connectionPromiseRef.current;
  };

  const attemptReconnect = () => {
    if (reconnectAttemptsRef.current >= 5) {
      return;
    }

    if (isConnected || isConnecting) {
      return;
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    const delay = 3000 * Math.pow(1.5, reconnectAttemptsRef.current);
    reconnectTimeoutRef.current = setTimeout(() => {
      reconnectAttemptsRef.current++;
      connect().catch((error) => {
        console.error(
          `[Socket] Reconnection attempt ${reconnectAttemptsRef.current} failed:`,
          error,
        );
      });
    }, delay);
  };

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.close(1000, 'Manual disconnect');
      socketRef.current = null;
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    connectionPromiseRef.current = null;
    setIsConnected(false);
    setIsConnecting(false);
    initializationRef.current = false;
  };

  const sendMessage = (type: string, data: any) => {
    if (!isConnected || !socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      console.warn('[Socket] Cannot send message - not connected:', {
        isConnected,
        hasSocket: !!socketRef.current,
        readyState: socketRef.current?.readyState,
      });
      return;
    }

    try {
      const message = JSON.stringify({ type, data });
      socketRef.current.send(message);
    } catch (error) {
      console.error('[Socket] Error sending message:', error);
    }
  };

  const onMessage = (type: string, handler: (data: any) => void) => {
    messageHandlersRef.current[type] = handler;
  };

  const removeMessageHandler = (type: string) => {
    delete messageHandlersRef.current[type];
  };

  useEffect(() => {
    if (!initializationRef.current) {
      initializationRef.current = true;
    }

    return () => {
      disconnect();
    };
  }, [profileId]);

  return {
    connect,
    disconnect,
    sendMessage,
    onMessage,
    removeMessageHandler,
    isConnected,
    isConnecting,
  };
};

export const useChatSocket = (profileId: string) => {
  const socketBaseUrl = 'localhost:4003/ws/chat';
  const initRef = useRef(false);

  if (!initRef.current) {
    initRef.current = true;
  }

  return useSocket(socketBaseUrl, profileId);
};
