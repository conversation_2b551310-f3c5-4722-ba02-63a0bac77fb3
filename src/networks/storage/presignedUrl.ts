/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import { FetchPresignedUrlPayloadI, FetchPresignedUrlResultI } from './types';

export const fetchPresignedUrlAPI = async (
  extensions: string[],
  folder: 'POST' | 'AVATAR' | 'SHIP' | 'PORT' | 'CERTIFICATION' | 'DOCUMENTATION',
): Promise<FetchPresignedUrlResultI[]> => {
  const result = await apiCall<FetchPresignedUrlPayloadI, FetchPresignedUrlResultI[]>(
    `/backend/api/v1/storage/presigned-url`,
    'POST',
    {
      isAuth: true,
      payload: {
        extensions,
        folder,
      },
    },
  );

  return result;
};
