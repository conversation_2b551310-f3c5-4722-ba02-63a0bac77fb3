/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import {
  DeleteConnectionParamsI,
  FetchConnectionsParamsI,
  FetchFollowersResponseI,
  SendConnectionPayloadI,
  SendConnectionResponseI,
} from './types';

export const sendConnectionRequestAPI = async (
  payload: SendConnectionPayloadI,
): Promise<SendConnectionResponseI[]> => {
  const result = await apiCall<SendConnectionPayloadI, SendConnectionResponseI[]>(
    '/backend/api/v1/network/request/send',
    'POST',
    {
      payload,
      isAuth: true,
    },
  );
  return result;
};

export const fetchConnectionsAPI = async ({
  profileId,
  cursorId,
  pageSize = 10,
}: FetchConnectionsParamsI) => {
  const query = { profileId, pageSize } as FetchConnectionsParamsI;
  query.cursorId = cursorId ?? null;

  const response = await apiCall<FetchConnectionsParamsI, FetchFollowersResponseI>(
    '/backend/api/v1/network/connections',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return response;
};

export const fetchMutualsAPI = async ({
  profileId,
  cursorId,
  pageSize = 10,
}: FetchConnectionsParamsI): Promise<FetchFollowersResponseI> => {
  const query: FetchConnectionsParamsI = { profileId, pageSize };
  query.cursorId = cursorId ?? null;

  const response = await apiCall<FetchConnectionsParamsI, FetchFollowersResponseI>(
    '/backend/api/v1/network/connections/mutual',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return response;
};

export const deleteConnectionAPI = async ({
  profileId,
}: DeleteConnectionParamsI): Promise<null> => {
  const result = await apiCall<DeleteConnectionParamsI, null>(
    `/backend/api/v1/network/connection`,
    'DELETE',
    {
      isAuth: true,
      routeId: profileId,
    },
  );
  return result;
};
