import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ObjUnknownI } from '@/src/types/common/data';
import { AuthorProfileI } from '@/src/networks/content/types';

export type RequestStatusTypeE = 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'REVOKED' | 'DISCONNECTED';

export interface CursorPaginationParamsI {
  cursorId?: number | null;
  pageSize?: number;
}

export interface StandardPaginationParamsI {
  page?: number;
  pageSize?: number;
}

export interface ProfileParamsI {
  profileId: string;
}

export interface FetchFollowersParamsI
  extends CursorPaginationParamsI,
    ProfileParamsI,
    ObjUnknownI {}

export interface FetchConnectionsParamsI
  extends CursorPaginationParamsI,
    ProfileParamsI,
    ObjUnknownI {}

export interface FetchRequestsParamsI extends StandardPaginationParamsI, ObjUnknownI {}

export interface RespondRequestParamsI extends ObjUnknownI {
  senderProfileId: string;
  requestedStatus: RequestStatusTypeE;
}

export interface SendConnectionPayloadI {
  receiverProfileId: string;
  requestedStatus: RequestStatusTypeE;
}

export interface SendConnectionResponseI {}

export interface DeleteConnectionParamsI {
  profileId: string;
}

export interface FetchFollowersDataI {
  status: string;
  cursorId: number;
  Profile: AuthorProfileI;
}

export interface PaginationResponseI {
  nextCursorId?: number | null;
  hasMore?: boolean;
}

export interface FetchFollowersResponseI extends PaginationResponseI {
  data: FetchFollowersDataI[];
  total: number;
}

export interface BlockProfileParamsI {
  toBlockId: string;
}

export interface UnblockProfileParamsI {
  toUnblockId: string;
}

export interface FetchBlockedProfilesParamsI extends ObjUnknownI {
  page?: number;
  pageSize?: number;
}

export interface BlockProfileResponseI {
  connectionsCount: number;
  followersCount: number;
  followingsCount: number;
}

export interface ProfileExternalI {
  id: string;
  name: string;
  avatar: string | null;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
}

export type FetchBlockedProfilesResponseI = {
  data: ProfileExternalI[];
  total: number;
};
