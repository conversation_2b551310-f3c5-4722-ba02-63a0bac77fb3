import { apiCall } from '@/src/services/api';
import { FetchUserProfileSkillsResultI } from './types';

export const fetchUserSkills = async (
  profileId: string,
  category: string,
): Promise<FetchUserProfileSkillsResultI[]> => {
  const result = await apiCall<string, FetchUserProfileSkillsResultI[]>(
    '/backend/api/v1/career/profile-skills',
    'GET',
    {
      isAuth: true,
      query: {
        profileId,
        category,
        pageSize: 3,
      },
    },
  );

  return result;
};
