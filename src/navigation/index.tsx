/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect, useState } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import AuthStackNavigator from '@/src/navigation/stacks/AuthStack';
import BottomTabNavigator from '@/src/navigation/tabs/TabNavigation';
import SplashScreen from '@/src/screens/SplashScreen';
import useSetup from '@/src/hooks/setup';
import type { RootStackParamListI } from './types';

const RootStack = createNativeStackNavigator<RootStackParamListI>();

const AppNavigator = () => {
  const { setup } = useSetup();
  const currentUser = useSelector(selectCurrentUser);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        await setup();
        setTimeout(() => {
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('App initialization error:', error);
        setIsLoading(false);
      }
    };

    initializeApp();
  }, [setup]);

  return (
    <RootStack.Navigator screenOptions={{ headerShown: false }}>
      {isLoading ? (
        <RootStack.Screen name="splash" component={SplashScreen} />
      ) : currentUser.isAuthenticated ? (
        <RootStack.Screen name="main" component={BottomTabNavigator} />
      ) : (
        <RootStack.Screen name="auth" component={AuthStackNavigator} />
      )}
    </RootStack.Navigator>
  );
};

export default AppNavigator;
