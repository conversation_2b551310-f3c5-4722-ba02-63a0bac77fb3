/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import NotificationScreen from '@/src/screens/Notifications';
import { withErrorBoundary } from '../../../hocs/withErrorBoundary';
import { withOfflineIndicator } from '../../../hocs/withOfflineIndicator';
import type { NotificationStackParamsListI, StackScreenI } from '../../types';

const NotificationScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(NotificationScreen, {
    title: 'Notifications Error',
    subtitle: 'Something went wrong loading your notifications. Please try again.',
  }),
);

export const screens: StackScreenI<NotificationStackParamsListI>[] = [
  { name: 'Notification', component: NotificationScreenWithErrorBoundary },
];
