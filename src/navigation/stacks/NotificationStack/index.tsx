/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { NotificationStackParamsListI } from '@/src/navigation/types';
import { screens } from './screen';

const NotificationStack = createStackNavigator<NotificationStackParamsListI>();

type NotificationRouteNames = keyof NotificationStackParamsListI;

const NotificationStackNavigator = ({
  initialRouteName = 'Notification',
}: {
  initialRouteName?: NotificationRouteNames;
}) => (
  <NotificationStack.Navigator
    screenOptions={{ headerShown: false }}
    initialRouteName={initialRouteName}
  >
    {screens.map(({ name, component }) => (
      <NotificationStack.Screen key={name} name={name} component={component} />
    ))}
  </NotificationStack.Navigator>
);

export default NotificationStackNavigator;
