/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { HomeStackParamListI } from '@/src/navigation/types';
import { screens } from './screen';

const HomeStack = createStackNavigator<HomeStackParamListI>();

type HomeRouteNames = keyof HomeStackParamListI;

const HomeStackNavigator = ({
  initialRouteName = 'Home',
}: {
  initialRouteName?: HomeRouteNames;
}) => (
  <HomeStack.Navigator screenOptions={{ headerShown: false }} initialRouteName={initialRouteName}>
    {screens.map(({ name, component }) => (
      <HomeStack.Screen key={name} name={name} component={component} />
    ))}
  </HomeStack.Navigator>
);

export default HomeStackNavigator;
