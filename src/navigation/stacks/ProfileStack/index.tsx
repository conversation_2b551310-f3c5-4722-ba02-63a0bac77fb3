/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createStackNavigator } from '@react-navigation/stack';
import { ProfileStackParamsListI } from '../../types';
import { screens } from './screen';

const ProfileStack = createStackNavigator<ProfileStackParamsListI>();

type ProfileRouteNames = keyof ProfileStackParamsListI;

const ProfileStackNavigator = ({
  initialRouteName = 'UserProfile',
}: {
  initialRouteName?: ProfileRouteNames;
}) => (
  <ProfileStack.Navigator
    screenOptions={{ headerShown: false }}
    initialRouteName={initialRouteName}
  >
    {screens.map(({ name, component }) => (
      <ProfileStack.Screen key={name} name={name} component={component} />
    ))}
  </ProfileStack.Navigator>
);

export default ProfileStackNavigator;
