/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import BlockedProfiles from '@/src/screens/BlockedProfiles';
import CommentScreen from '@/src/screens/Comment';
import ConnectionScreen from '@/src/screens/Connection';
import EditCargoItemScreen from '@/src/screens/EditCargoItem';
import EditCertificationItemScreen from '@/src/screens/EditCertificationItem';
import EditCertificationListScreen from '@/src/screens/EditCertificationList';
import EditDetailScreen from '@/src/screens/EditDetail';
import EditDocumentItemScreen from '@/src/screens/EditDocumentItem';
import EditDocumentsListScreen from '@/src/screens/EditDocumentsList';
import EditEducationItemScreen from '@/src/screens/EditEducationItem';
import EditEducationListScreen from '@/src/screens/EditEducationList';
import EditEquipmentItemScreen from '@/src/screens/EditEquipmentItem';
import EditExperienceItemScreen from '@/src/screens/EditExperienceItem';
import EditExperienceListScreen from '@/src/screens/EditExperienceList';
import EditShipItemScreen from '@/src/screens/EditShipItem';
import EditSkillsListScreen from '@/src/screens/EditSkillsList';
import EditUserProfileScreen from '@/src/screens/EditUserProfile';
import EntitySearchScreen from '@/src/screens/EntitySearch';
import LikesScreen from '@/src/screens/Likes';
import PortsVisitedScreen from '@/src/screens/PortsVisited';
import PrivacyPolicyScreen from '@/src/screens/PrivacyPolicy';
import ShipProfileScreen from '@/src/screens/ShipProfile';
import TermsOfServiceScreen from '@/src/screens/TermsAndCondition';
import UserProfileScreen from '@/src/screens/UserProfile';
import UserSettingScreen from '@/src/screens/UserSettings';
import { withErrorBoundary } from '../../../hocs/withErrorBoundary';
import { withOfflineIndicator } from '../../../hocs/withOfflineIndicator';
import type { ProfileStackParamsListI, StackScreenI } from '../../types';

const UserProfileScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(UserProfileScreen, {
    title: 'Profile Error',
    subtitle: 'Something went wrong loading the profile. Please try again.',
  }),
);

const ConnectionScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(ConnectionScreen, {
    title: 'Connections Error',
    subtitle: 'Something went wrong loading connections. Please try again.',
  }),
);

const LikesScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(LikesScreen, {
    title: 'Likes Error',
    subtitle: 'Something went wrong loading likes. Please try again.',
  }),
);

const CommentScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(CommentScreen, {
    title: 'Comments Error',
    subtitle: 'Something went wrong loading comments. Please try again.',
  }),
);

const UserSettingScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(UserSettingScreen, {
    title: 'Settings Error',
    subtitle: 'Something went wrong loading settings. Please try again.',
  }),
);

const BlockedProfilesWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(BlockedProfiles, {
    title: 'Blocked Profiles Error',
    subtitle: 'Something went wrong loading blocked profiles. Please try again.',
  }),
);

const EditUserProfileScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditUserProfileScreen, {
    title: 'Edit Profile Error',
    subtitle: 'Something went wrong while editing your profile. Please try again.',
  }),
);

const EntitySearchScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EntitySearchScreen, {
    title: 'Search Error',
    subtitle: 'Something went wrong during search. Please try again.',
  }),
);

const EditDetailScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditDetailScreen, {
    title: 'Edit Details Error',
    subtitle: 'Something went wrong while editing details. Please try again.',
  }),
);

const EditDocumentsListScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditDocumentsListScreen, {
    title: 'Documents Error',
    subtitle: 'Something went wrong loading your documents. Please try again.',
  }),
);

const EditDocumentItemScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditDocumentItemScreen, {
    title: 'Document Edit Error',
    subtitle: 'Something went wrong while editing the document. Please try again.',
  }),
);

const EditCertificationListScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditCertificationListScreen, {
    title: 'Certifications Error',
    subtitle: 'Something went wrong loading your certifications. Please try again.',
  }),
);

const EditCertificationItemScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditCertificationItemScreen, {
    title: 'Certification Edit Error',
    subtitle: 'Something went wrong while editing the certification. Please try again.',
  }),
);

const EditExperienceListScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditExperienceListScreen, {
    title: 'Experience Error',
    subtitle: 'Something went wrong loading your experience. Please try again.',
  }),
);

const EditExperienceItemScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditExperienceItemScreen, {
    title: 'Experience Edit Error',
    subtitle: 'Something went wrong while editing the experience. Please try again.',
  }),
);

const EditEducationListScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditEducationListScreen, {
    title: 'Education Error',
    subtitle: 'Something went wrong loading your education. Please try again.',
  }),
);

const EditEducationItemScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditEducationItemScreen, {
    title: 'Education Edit Error',
    subtitle: 'Something went wrong while editing the education. Please try again.',
  }),
);

const EditSkillsListScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditSkillsListScreen, {
    title: 'Skills Error',
    subtitle: 'Something went wrong loading your skills. Please try again.',
  }),
);

const PortsVisitedScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(PortsVisitedScreen, {
    title: 'Ports Visited Error',
    subtitle: 'Something went wrong loading ports visited. Please try again.',
  }),
);

const EditShipItemScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditShipItemScreen, {
    title: 'Ship Edit Error',
    subtitle: 'Something went wrong while editing the ship details. Please try again.',
  }),
);

const EditEquipmentItemScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditEquipmentItemScreen, {
    title: 'Equipment Edit Error',
    subtitle: 'Something went wrong while editing the equipment. Please try again.',
  }),
);

const EditCargoItemScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EditCargoItemScreen, {
    title: 'Cargo Edit Error',
    subtitle: 'Something went wrong while editing the cargo details. Please try again.',
  }),
);

const ShipProfileScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(ShipProfileScreen, {
    title: 'Ship Profile Error',
    subtitle: 'Something went wrong loading the ship profile. Please try again.',
  }),
);

const PrivacyPolicyScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(PrivacyPolicyScreen, {
    title: 'Privacy Policy Error',
    subtitle: 'Something went wrong loading the privacy policy. Please try again.',
  }),
);

const TermsOfServiceScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(TermsOfServiceScreen, {
    title: 'Terms of Service Error',
    subtitle: 'Something went wrong loading the terms of service. Please try again.',
  }),
);

export const screens: StackScreenI<ProfileStackParamsListI>[] = [
  { name: 'UserProfile', component: UserProfileScreenWithErrorBoundary },
  { name: 'OtherUserProfile', component: UserProfileScreenWithErrorBoundary },
  { name: 'Connection', component: ConnectionScreenWithErrorBoundary },
  { name: 'Likes', component: LikesScreenWithErrorBoundary },
  { name: 'Comment', component: CommentScreenWithErrorBoundary },
  { name: 'UserSettings', component: UserSettingScreenWithErrorBoundary },
  { name: 'BlockedUserProfiles', component: BlockedProfilesWithErrorBoundary },
  { name: 'EditUserProfile', component: EditUserProfileScreenWithErrorBoundary },
  { name: 'SearchScreen', component: EntitySearchScreenWithErrorBoundary },
  { name: 'EditDetail', component: EditDetailScreenWithErrorBoundary },
  { name: 'EditDocumentList', component: EditDocumentsListScreenWithErrorBoundary },
  { name: 'EditDocumentItem', component: EditDocumentItemScreenWithErrorBoundary },
  { name: 'EditCertificationList', component: EditCertificationListScreenWithErrorBoundary },
  { name: 'EditCertificationItem', component: EditCertificationItemScreenWithErrorBoundary },
  { name: 'EditExperienceList', component: EditExperienceListScreenWithErrorBoundary },
  { name: 'EditExperienceItem', component: EditExperienceItemScreenWithErrorBoundary },
  { name: 'EditEducationList', component: EditEducationListScreenWithErrorBoundary },
  { name: 'EditEducationItem', component: EditEducationItemScreenWithErrorBoundary },
  { name: 'EditSkillsList', component: EditSkillsListScreenWithErrorBoundary },
  { name: 'PortsVisited', component: PortsVisitedScreenWithErrorBoundary },
  { name: 'EditShipItem', component: EditShipItemScreenWithErrorBoundary },
  { name: 'EditEquipmentItem', component: EditEquipmentItemScreenWithErrorBoundary },
  { name: 'EditCargoItem', component: EditCargoItemScreenWithErrorBoundary },
  { name: 'ShipProfile', component: ShipProfileScreenWithErrorBoundary },
  { name: 'Privacy', component: PrivacyPolicyScreenWithErrorBoundary },
  { name: 'Terms', component: TermsOfServiceScreenWithErrorBoundary },
];
