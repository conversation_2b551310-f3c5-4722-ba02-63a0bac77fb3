/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import AddUserDetailsScreen from '@/src/screens/AddUserDetail';
import CreateAccountScreen from '@/src/screens/CreateAccount';
import CreateAccountSuccessScreen from '@/src/screens/CreateAccountSuccess';
import EntitySearchScreen from '@/src/screens/EntitySearch';
import OnboardingScreen from '@/src/screens/Onboarding';
import SetUsernameScreen from '@/src/screens/SetUsername';
import UserLoginScreen from '@/src/screens/UserLogin';
import { withErrorBoundary } from '../../../hocs/withErrorBoundary';
import { withOfflineIndicator } from '../../../hocs/withOfflineIndicator';
import type { AuthStackParamListI, StackScreenI } from '../../types';

const OnboardingScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(OnboardingScreen, {
    title: 'Onboarding Error',
    subtitle: 'Something went wrong during onboarding. Please try again.',
  }),
);

const UserLoginScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(UserLoginScreen, {
    title: 'Login Error',
    subtitle: 'Something went wrong during login. Please try again.',
  }),
);

const CreateAccountScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(CreateAccountScreen, {
    title: 'Account Creation Error',
    subtitle: 'Something went wrong while creating your account. Please try again.',
  }),
);

const SetUsernameScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(SetUsernameScreen, {
    title: 'Username Setup Error',
    subtitle: 'Something went wrong while setting up your username. Please try again.',
  }),
);

const AddUserDetailsScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(AddUserDetailsScreen, {
    title: 'User Details Error',
    subtitle: 'Something went wrong while adding your details. Please try again.',
  }),
);

const EntitySearchScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EntitySearchScreen, {
    title: 'Search Error',
    subtitle: 'Something went wrong during search. Please try again.',
  }),
);

const CreateAccountSuccessScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(CreateAccountSuccessScreen, {
    title: 'Success Error',
    subtitle: 'Something went wrong on the success screen. Please try again.',
  }),
);

export const screens: StackScreenI<AuthStackParamListI>[] = [
  { name: 'Onboarding', component: OnboardingScreenWithErrorBoundary },
  { name: 'UserLogin', component: UserLoginScreenWithErrorBoundary },
  { name: 'CreateAccount', component: CreateAccountScreenWithErrorBoundary },
  { name: 'SetUsername', component: SetUsernameScreenWithErrorBoundary },
  { name: 'AddUserDetailScreen', component: AddUserDetailsScreenWithErrorBoundary },
  { name: 'SearchScreen', component: EntitySearchScreenWithErrorBoundary },
  { name: 'CreateAccountSuccess', component: CreateAccountSuccessScreenWithErrorBoundary },
];
