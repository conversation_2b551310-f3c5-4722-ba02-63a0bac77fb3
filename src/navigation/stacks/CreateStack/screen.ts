/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import CreateContentScreen from '@/src/screens/CreateContent';
import { withErrorBoundary } from '../../../hocs/withErrorBoundary';
import { withOfflineIndicator } from '../../../hocs/withOfflineIndicator';
import type { CreateStackParamsListI, StackScreenI } from '../../types';

const CreateContentScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(CreateContentScreen, {
    title: 'Content Creation Error',
    subtitle: 'Something went wrong while creating content. Please try again.',
  }),
);

export const screens: StackScreenI<CreateStackParamsListI>[] = [
  { name: 'CreateContent', component: CreateContentScreenWithErrorBoundary },
];
