/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path, Rect } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const AddItem: React.FC<Omit<OutlinedIconPropsI, 'onPress'>> = ({
  width = 3.96,
  height = 3.96,
  color = '#448600',
  fill,
  stroke = '#D4D4D4',
  strokeWidth = 1,
  accessibilityLabel = 'Add Item',
  disabled,
  ...props
}) => {
  const fillColor = fill || color;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 32 32"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Rect
        x="0.5"
        y="0.5"
        width={RFPercentage(3.8)}
        height={RFPercentage(3.8)}
        rx="7.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
      />
      <Path
        d="M24 16C24 16.1768 23.9298 16.3464 23.8047 16.4714C23.6797 16.5964 23.5101 16.6667 23.3333 16.6667H16.6667V23.3333C16.6667 23.5101 16.5964 23.6797 16.4714 23.8047C16.3464 23.9298 16.1768 24 16 24C15.8232 24 15.6536 23.9298 15.5286 23.8047C15.4036 23.6797 15.3333 23.5101 15.3333 23.3333V16.6667H8.66667C8.48986 16.6667 8.32029 16.5964 8.19526 16.4714C8.07024 16.3464 8 16.1768 8 16C8 15.8232 8.07024 15.6536 8.19526 15.5286C8.32029 15.4036 8.48986 15.3333 8.66667 15.3333H15.3333V8.66667C15.3333 8.48986 15.4036 8.32029 15.5286 8.19526C15.6536 8.07024 15.8232 8 16 8C16.1768 8 16.3464 8.07024 16.4714 8.19526C16.5964 8.32029 16.6667 8.48986 16.6667 8.66667V15.3333H23.3333C23.5101 15.3333 23.6797 15.4036 23.8047 15.5286C23.9298 15.6536 24 15.8232 24 16Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default AddItem;
