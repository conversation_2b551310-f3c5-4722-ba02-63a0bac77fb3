import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import BackButton from '@/src/components/BackButton';
import ChipInput from '@/src/components/ChipInput';
import { EditSkillsListPropsI } from './types';
import { useEditSkillsList } from './useHook';

const EditSkillsList = ({ onBack, profileId, category }: EditSkillsListPropsI) => {
  const { localSkills, isSubmitting, handleSubmit, setLocalSkills, loading } =
    useEditSkillsList(profileId);

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <View>
      <View className="flex-row items-center justify-between px-4">
        <View className="flex-row items-center gap-1">
          <BackButton onBack={onBack} label="" />
          <Text className="text-xl font-medium">Edit Skills</Text>
        </View>
        <Pressable onPress={handleSubmit} disabled={isSubmitting}>
          <Text className="text-lg font-medium text-[#448600]">
            {isSubmitting ? 'Saving...' : 'Save'}
          </Text>
        </Pressable>
      </View>
      <ScrollView className="px-2 py-4">
        <ChipInput
          showTitle={false}
          chips={localSkills}
          title="Skills"
          onRemove={(id) => setLocalSkills((prev) => prev.filter((s) => s.id !== id))}
        />
      </ScrollView>
    </View>
  );
};

export default EditSkillsList;
