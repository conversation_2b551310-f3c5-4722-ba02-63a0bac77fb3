import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import { clearMultipleSelections } from '@/src/redux/slices/entitysearch/searchSlice';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { addSkills, deleteSkillsAPI, fetchSkillsAPI } from '@/src/networks/career/skills';
import { SkillI, useEditSkillsListI } from './types';

export const useEditSkillsList = (profileId: string): useEditSkillsListI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [localSkills, setLocalSkills] = useState<SkillI[]>([]);
  const [initialSkills, setInitialSkills] = useState<SkillI[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const skillsSelection = useSelector(
    selectMultipleSelectionsByKey('skill'),
  ) as unknown as SkillI[];
  const dispatch = useDispatch();
  const navigation = useNavigation();

  useEffect(() => {
    const fetchSkillsList = async () => {
      try {
        setLoading(true);
        const response = await fetchSkillsAPI(profileId);
        setLocalSkills(response);
        setInitialSkills(response);
      } catch (error) {
        triggerErrorBoundary(
          new Error(
            'Failed to load skills: ' + (error instanceof Error ? error.message : 'Unknown error'),
          ),
        );
      } finally {
        setLoading(false);
      }
    };

    fetchSkillsList();
  }, [profileId]);

  useEffect(() => {
    if (!skillsSelection) return;

    setLocalSkills((prev) => {
      const existingIds = new Set(prev.map((s) => s.id));
      const merged = [...prev, ...skillsSelection.filter((s) => !existingIds.has(s.id))];
      return merged;
    });
  }, [skillsSelection]);

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      const payload = transformData(localSkills);
      await addSkills(payload);
      const deletedSkills = initialSkills
        .filter((initial) => !localSkills.some((local) => local.id === initial.id))
        .map(({ id, dataType }) => ({ id, dataType }));

      if (deletedSkills.length > 0) {
        await deleteSkillsAPI(deletedSkills);
        showToast({
          message: 'Success',
          description: `Skill(s) deleted sucessfully`,
          type: 'success',
        });
      } else {
        showToast({
          message: 'Success',
          description: `Skill(s) added sucessfully`,
          type: 'success',
        });
      }
      dispatch(clearMultipleSelections('skill'));
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Skill(s)',
            description: 'Unable to save Skills',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    localSkills,
    isSubmitting,
    handleSubmit,
    setLocalSkills,
    loading,
  };
};

const transformData = (data: SkillI[]) => {
  return data.map((item: SkillI) => {
    return {
      id: item.id,
      dataType: item.dataType,
      category: item.category,
    };
  });
};
