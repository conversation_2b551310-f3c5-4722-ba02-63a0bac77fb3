import { Dispatch, SetStateAction } from 'react';
import { StackNavigationProp } from '@react-navigation/stack';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export interface useEditSkillsListI {
  localSkills: SkillI[];
  isSubmitting: boolean;
  handleSubmit: () => void;
  setLocalSkills: Dispatch<SetStateAction<SkillI[]>>;
  loading: boolean;
}

export interface EditSkillsListPropsI {
  onBack: () => void;
  profileId: string;
  category: string;
}

export interface SkillI {
  id: string;
  name: string;
  dataType: string;
  category: string;
}
