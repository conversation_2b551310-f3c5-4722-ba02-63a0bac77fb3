/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { StackNavigationProp } from '@react-navigation/stack';
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdTitleI } from '@/src/types/common/data';
import { ProfileStackParamsListI } from '@/src/navigation/types';

export interface DocumentFormDataI {
  documentType: string;
  documentName: string;
  documentNumber: string;
  country: Omit<SearchResultI, 'dataType'> & { dataType?: 'raw' | 'master' };
  validFrom: string;
  validUntil: string;
  documentFile?: string;
}

export interface UseEditDocumentItemI {
  methods: UseFormReturn<DocumentFormDataI>;
  documentTypeOptions: IdTitleI[];
  documentNameOptions: IdTitleI[];
  isSubmitting: boolean;
  onSubmit: (data: DocumentFormDataI) => Promise<void>;
  navigation: StackNavigationProp<ProfileStackParamsListI>;
  handleAttachment: () => void;
  selectedFile: string | null;
  loading: boolean;
  handleDownload: () => void;
  handleRemoveFile: () => void;
  isFileRemoved: boolean;
  clearFields: () => void;
}

export interface EditDocumentItemPropsI {
  onBack: () => void;
  profileId: string;
  documentId?: string;
  type: string;
}

export interface CompressedFileI {
  uri: string;
  type: string;
  filename: string;
}

type PreSignedUrlI = {
  accessUrl: string;
  extension: string;
  uploadUrl: string;
};

export type PreSignedUrlIResponse = PreSignedUrlI[];

export interface DocumentI {
  country: {
    iso2: string;
    name: string;
  };
  documentNumber: string;
  expiryStatus: string;
  fileUrl: string;
  fromDate: string;
  id: string;
  type?: string;
  untilDate: string;
}

export type DocTypeI = 'CDC' | 'PASSPORT' | 'SID';

export type IdentityDocPayloadI = {
  documentNo: string;
  type: DocTypeI;
  countryIso2: string;
  fromDate: string;
  untilDate: string;
  file?: {
    opr: 'CREATE' | 'UPDATE' | 'DELETE';
    fileUrl?: string;
  };
};

export type VisaDocPayloadI = {
  documentNo: string;
  name: string;
  countryIso2: string;
  fromDate: string;
  untilDate: string;
  file?: {
    opr: 'CREATE' | 'UPDATE' | 'DELETE';
    fileUrl?: string;
  };
  type?: string;
};
