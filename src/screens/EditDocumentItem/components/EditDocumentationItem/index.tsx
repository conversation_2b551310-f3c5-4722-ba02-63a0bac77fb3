/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import PdfDownload from '@/src/components/PdfDownload';
import Select from '@/src/components/Select';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import TrashBin from '@/src/assets/svgs/TrashBin';
import Upload from '@/src/assets/svgs/Upload';
import { EditDocumentItemPropsI } from './types';
import { useEditDocumentItem } from './useHook';

export const EditDocumentItem = ({
  onBack,
  profileId,
  documentId,
  type,
}: EditDocumentItemPropsI) => {
  const countrySelection = useSelector(selectSelectionByKey('country'));

  const {
    methods,
    documentTypeOptions,
    documentNameOptions,
    isSubmitting,
    onSubmit,
    handleAttachment,
    selectedFile,
    loading,
    handleDownload,
    handleRemoveFile,
    isFileRemoved,
    clearFields,
  } = useEditDocumentItem(profileId, documentId, type);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = methods;

  useEffect(() => {
    if (countrySelection) {
      methods.setValue('country', countrySelection);
    }

    return () => {
      clearFields();
    };
  }, [countrySelection, methods]);

  const FromDate = watch('validFrom');
  const toDate = watch('validUntil');

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton onBack={onBack} label="Edit documents" />
          <Pressable onPress={handleSubmit(onSubmit)} disabled={isSubmitting}>
            <Text className="text-lg font-medium text-[#448600]">
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>

        <View className="mb-6">
          <Text className="mb-2 text-base font-medium">Document type</Text>
          <Controller
            control={control}
            name="documentType"
            rules={{ required: 'Document type is required' }}
            render={({ field: { onChange, value } }) => (
              <Select
                options={documentTypeOptions}
                value={value}
                onChange={onChange}
                placeholder="Select document type"
                error={errors.documentType?.message}
              />
            )}
          />
        </View>

        <View className="mb-6">
          {methods.watch('documentType').toLowerCase() !== 'identity' ? (
            <Controller
              control={control}
              name="documentName"
              rules={{
                required: 'Visa type is required',
              }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  label="Visa Type"
                  placeholder="Enter type"
                  value={value}
                  onChangeText={onChange}
                  editable
                />
              )}
            />
          ) : (
            <>
              <Text className="mb-2 text-base font-medium">Document name</Text>
              <Controller
                control={control}
                name="documentName"
                rules={{ required: 'Document name is required' }}
                render={({ field: { onChange, value } }) => (
                  <Select
                    options={documentNameOptions}
                    value={value}
                    onChange={onChange}
                    placeholder="Select document name"
                    error={errors.documentName?.message}
                  />
                )}
              />
            </>
          )}
        </View>

        <View className="mb-6">
          <Text className="mb-2 text-base font-medium">Upload document</Text>
          {selectedFile || (methods.watch('documentFile') && !isFileRemoved) ? (
            <View className="border border-dashed border-[#E5E5E5] rounded-lg p-6">
              <View className="flex-row items-center justify-between">
                <View className="flex-1">
                  {selectedFile ? (
                    <Text className="text-sm font-medium">{selectedFile}</Text>
                  ) : (
                    <PdfDownload onDownload={handleDownload} />
                  )}
                </View>
                <View className="flex-row ml-3">
                  <Pressable onPress={handleAttachment} className="mr-3 p-2">
                    <Upload width={2} height={2} />
                  </Pressable>
                  <Pressable onPress={handleRemoveFile} className="p-2">
                    <TrashBin width={2} height={2} color="#DC2626" />
                  </Pressable>
                </View>
              </View>
            </View>
          ) : isFileRemoved ? (
            <View className="border border-dashed border-[#E5E5E5] rounded-lg p-6">
              <View className="flex-row items-center justify-between">
                <Text className="text-sm text-red-600 flex-1">File marked for removal</Text>
                <Pressable onPress={handleAttachment} className="p-2">
                  <Upload width={2} height={2} />
                </Pressable>
              </View>
            </View>
          ) : (
            <Pressable
              className="border border-dashed border-[#E5E5E5] rounded-lg p-6 items-center"
              onPress={handleAttachment}
            >
              <Upload />
              <Text className="mt-2 font-medium">Upload file</Text>
              <Text style={{ fontSize: 12, color: '#6B7280' }}>
                Supported file types:
                <Text style={{ fontWeight: '500', color: '#4B5563' }}> .pdf </Text>
                (Max 5 MB),
                <Text style={{ fontWeight: '500', color: '#4B5563' }}> .jpg </Text>
                and
                <Text style={{ fontWeight: '500', color: '#4B5563' }}> .jpeg </Text>
                (Max 2 MB).
              </Text>
            </Pressable>
          )}
        </View>

        <View className="mb-3">
          <Controller
            control={control}
            name="documentNumber"
            rules={{
              required: 'Document number is required',
              minLength: {
                value: 3,
                message: 'Document number must be at least 3 characters',
              },
            }}
            render={({ field: { onChange, value } }) => (
              <TextInput
                label="Document number"
                placeholder="Enter document number"
                value={value}
                onChangeText={onChange}
                error={errors.documentNumber?.message}
              />
            )}
          />
        </View>

        <Controller
          control={control}
          name="country"
          rules={{ required: 'Country is required' }}
          render={() => (
            <EntitySearch
              placeholder="Search country"
              selectionKey="country"
              title="Country"
              data={countrySelection ? countrySelection.name : methods.watch('country').name}
            />
          )}
        />

        <View className="flex-row mb-6">
          <View className="flex-1 mr-2">
            <Controller
              control={control}
              name="validFrom"
              rules={{ required: 'Valid from date is required' }}
              render={({ field: { onChange } }) => (
                <DatePicker
                  title="Valid from"
                  selectedDate={FromDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                />
              )}
            />
          </View>
          <View className="flex-1 ml-2">
            <Controller
              control={control}
              name="validUntil"
              rules={{ required: 'Valid until date is required' }}
              render={({ field: { onChange } }) => (
                <DatePicker
                  title="Valid until"
                  selectedDate={toDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                />
              )}
            />
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default EditDocumentItem;
