/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditDocumentItem from './components/EditDocumentationItem';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditDocumentItem'>;

const EditDocumentItemScreen = () => {
  const route = useRoute<RouteProps>();
  const { profileId, documentId, type } = route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <EditDocumentItem
        onBack={() => navigation.goBack()}
        profileId={profileId!}
        documentId={documentId}
        type={type!}
      />
    </SafeArea>
  );
};

export default EditDocumentItemScreen;
