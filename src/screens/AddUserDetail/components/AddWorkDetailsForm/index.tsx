/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useCallback, useEffect } from 'react';
import { Text, View } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import EntitySearch from '@/src/components/EntitySearch';
import ProgressBar from '@/src/components/Progress';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { NON_ENTITY_EMPLOYEE_DESIGNATION_IDS } from '@/src/consts/designation';
import { WorkDetailsFormDataI, WorkDetailsPropsI } from './types';
import useWorkDetails from './useHook';

const WorkDetails = ({ onNext, onBack, personalData, initialData }: WorkDetailsPropsI) => {
  const { methods, isSubmitting, onSubmit, handleBack } = useWorkDetails(
    onNext,
    personalData,
    onBack,
  );

  const {
    control,
    handleSubmit,
    setValue,
    formState: { isValid },
    trigger,
  } = methods;

  const designationSelection = useSelector(selectSelectionByKey('designation'));
  const entitySelection = useSelector(selectSelectionByKey('entity'));
  const currentUser = useSelector(selectCurrentUser);

  useEffect(() => {
    if (initialData) {
      Object.entries(initialData).forEach(([key, value]) => {
        setValue(key as keyof WorkDetailsFormDataI, value);
      });
      trigger();
    } else if (currentUser) {
      if (currentUser.designation) {
        setValue('designation', currentUser.designation);
      }
      if (currentUser.organisation) {
        setValue('entity', currentUser.organisation);
      }
    }
  }, [initialData]);

  useFocusEffect(
    useCallback(() => {
      if (entitySelection) {
        setValue('entity', entitySelection);
        trigger('entity');
      }
      if (designationSelection) {
        setValue('designation', designationSelection);
        trigger('designation');
      }
    }, [entitySelection?.id, designationSelection?.id]),
  );

  const shouldShowOrg =
    designationSelection && !NON_ENTITY_EMPLOYEE_DESIGNATION_IDS.has(designationSelection.id);

  return (
    <View className="flex-1 bg-white">
      <BackButton onBack={handleBack} />
      <View className="my-4">
        <Text className="text-xl font-bold leading-8">
          Tell us about your professional background
        </Text>
      </View>
      <View className="mt-2 flex flex-row items-center gap-4">
        <Text className="text-base font-medium leading-6">Work details (2/2)</Text>
        <ProgressBar progress={100} />
      </View>
      <View className="mt-5 flex flex-col gap-4">
        <Controller
          control={control}
          name="designation"
          rules={{ required: 'Designation is required' }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              placeholder="Search designation"
              selectionKey="designation"
              title="Designation"
              data={designationSelection?.name || ''}
              error={error?.message}
            />
          )}
        />
        {shouldShowOrg && (
          <Controller
            control={control}
            name="entity"
            rules={{ required: 'Entity is required' }}
            render={({ fieldState: { error } }) => (
              <EntitySearch
                placeholder="Search organisation"
                selectionKey="entity"
                title="Organisation"
                data={entitySelection?.name || ''}
                error={error?.message}
              />
            )}
          />
        )}
      </View>
      <View className="mt-8">
        <Button
          label="Finish"
          onPress={handleSubmit(onSubmit)}
          variant={isValid ? 'primary' : 'tertiary'}
          disabled={!isValid || isSubmitting}
          loading={isSubmitting}
        />
      </View>
    </View>
  );
};

export default WorkDetails;
