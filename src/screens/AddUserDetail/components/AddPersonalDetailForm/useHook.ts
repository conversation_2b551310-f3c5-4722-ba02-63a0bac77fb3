/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { setProfileAsync } from '@/src/redux/slices/user/userSlice';
import { updateProfileDetail } from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { onboardingPersonalAPI } from '@/src/networks/onboarding/personal';
import { PersonalDetailsFormDataI, UsePersonalDetailsFormI } from './types';

const usePersonalDetails = (
  onNext: (data: PersonalDetailsFormDataI) => void,
): UsePersonalDetailsFormI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch<AppDispatch>();

  const currentUser = useSelector(selectCurrentUser);
  const countrySelection = useSelector(selectSelectionByKey('country'));

  const methods = useForm<PersonalDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      fullName: currentUser?.fullName || '',
      country: countrySelection,
      gender: currentUser?.gender || '',
    },
  });

  const onSubmit = async (data: PersonalDetailsFormDataI) => {
    try {
      setIsSubmitting(true);

      const payload = {
        countryIso2: data.country?.id,
        fullName: data.fullName,
        gender: data.gender.toUpperCase(),
      };

      await onboardingPersonalAPI(payload);
      dispatch(
        setProfileAsync({
          country: countrySelection,
          fullName: data.fullName,
          gender: data.gender,
        }),
      );
      dispatch(updateProfileDetail());
      onNext(data);
    } catch (error) {
      handleError(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
  };
};

export default usePersonalDetails;
