/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect } from 'react';
import { Text, View } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import EntitySearch from '@/src/components/EntitySearch';
import ProgressBar from '@/src/components/Progress';
import Select from '@/src/components/Select';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { PersonalDetailsFormDataI, PersonalDetailsPropsI } from './types';
import usePersonalDetails from './useHook';

const GENDER_OPTIONS = [
  { title: 'Male', id: 'm' },
  { title: 'Female', id: 'f' },
  { title: 'Other', id: 'o' },
];

const PersonalDetails = ({ onNext, onBack, initialData }: PersonalDetailsPropsI) => {
  const { methods, isSubmitting, onSubmit } = usePersonalDetails(onNext);

  const {
    control,
    handleSubmit,
    setValue,
    trigger,
    formState: { isValid },
  } = methods;

  const countrySelection = useSelector(selectSelectionByKey('country'));

  useEffect(() => {
    if (initialData) {
      Object.entries(initialData).forEach(([key, value]) => {
        setValue(key as keyof PersonalDetailsFormDataI, value);
      });
      trigger();
    }
  }, [initialData, setValue, trigger]);

  useFocusEffect(() => {
    if (countrySelection) {
      setValue('country', countrySelection);
      trigger('country');
    }
  });

  return (
    <View className="flex-1 bg-white">
      {onBack && <BackButton onBack={onBack} />}
      <View className="my-4">
        <Text className="text-xl font-bold leading-6">We need a few more details to proceed</Text>
      </View>
      <View className="mt-2 flex flex-row items-center gap-4">
        <Text className="text-base font-medium leading-6">Personal details (1/2)</Text>
        <ProgressBar progress={50} />
      </View>
      <View className="mt-5 flex flex-col gap-4">
        <Controller
          control={control}
          name="fullName"
          rules={{ required: 'Full name is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <TextInput
              label="Full name"
              value={value}
              onChangeText={(val) => {
                onChange(val);
                trigger('fullName');
              }}
              placeholder="Enter full name"
              error={error?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="gender"
          rules={{ required: 'Gender is required' }}
          render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
            <Select
              ref={ref}
              label="Gender"
              options={GENDER_OPTIONS}
              value={value}
              onChange={(val) => {
                onChange(val);
                trigger('gender');
              }}
              placeholder="Select gender"
              error={error?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="country"
          rules={{ required: 'Country is required' }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              placeholder="Search country"
              selectionKey="country"
              title="Country"
              data={countrySelection?.name || ''}
              error={error?.message}
            />
          )}
        />
      </View>
      <View className="mt-8">
        <Button
          label="Next"
          onPress={handleSubmit(onSubmit)}
          variant={isValid ? 'primary' : 'tertiary'}
          disabled={!isValid || isSubmitting}
          loading={isSubmitting}
        />
      </View>
    </View>
  );
};

export default PersonalDetails;
