/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { View } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import Tabs from '@/src/components/Tabs';
import { ListItem } from '@/src/components/UsersList/types';
import {
  AppStackParamListI,
  BottomTabNavigationI,
  ProfileStackParamsListI,
} from '@/src/navigation/types';
import ConnectionsList from './components/ConnectionsList';

const Connection = () => {
  const route = useRoute<RouteProp<ProfileStackParamsListI, 'Connection'>>();
  const { isUserProfile, profileId, type } = route.params;
  const navigation = useNavigation<BottomTabNavigationI>();
  const isConnections = type === 'connections';
  const isRequests = type.startsWith('requests_');
  const showTabs = !isConnections;
  const state = navigation.getState();
  const activeRouteName = state.routes[state.index].name as keyof AppStackParamListI;

  let tabs = [];

  if (isUserProfile) {
    if (isConnections) {
      tabs = [{ id: 'connections', label: 'Connections' }];
    } else if (isRequests) {
      tabs = [
        { id: 'requests_received', label: 'Received Requests' },
        { id: 'requests_sent', label: 'Sent Requests' },
      ];
    } else {
      tabs = [
        { id: 'followers', label: 'Followers' },
        { id: 'following', label: 'Following' },
      ];
    }
  } else {
    tabs = [
      { id: 'followers', label: 'Followers' },
      { id: 'mutuals', label: 'Mutuals' },
    ];
  }

  const [activeTab, setActiveTab] = useState<string>(type ?? tabs[0].id);

  const handleUserPress = (item: ListItem) => {
    if (activeRouteName === 'Home') {
      navigation.navigate('HomeStack', {
        screen: 'OtherUserProfile',
        params: { profileId: item.Profile.id, fromTabPress: false },
      });
    } else {
      navigation.navigate('ProfileStack', {
        screen: 'UserProfile',
        params: { profileId: item.Profile.id, fromTabPress: false },
      });
    }
  };

  const onBack = () => navigation.goBack();

  return (
    <SafeArea>
      <View className="flex-row items-center px-4">
        <BackButton onBack={onBack} label="" />
      </View>
      {showTabs && <Tabs activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />}
      <ConnectionsList profileId={profileId} type={activeTab} onUserPress={handleUserPress} />
    </SafeArea>
  );
};

export default Connection;
