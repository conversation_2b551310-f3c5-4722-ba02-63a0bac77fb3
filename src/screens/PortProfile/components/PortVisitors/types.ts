import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export interface VisitorItem {
  id: string;
  name: string;
  avatar: string | null;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
}

export interface FetchVisitorsResult {
  total: number;
  data: VisitorItem[];
}

export interface FetchVisitorsParams {
  unLocode: string;
  dataType: 'master' | 'raw';
  page: number;
  pageSize: number;
}

export interface PortVisitorsProps {
  unLocode: string;
  dataType: 'master' | 'raw';
}

export interface UsePortVisitorsParams {
  unLocode: string;
  dataType: 'master' | 'raw';
  pageSize?: number;
}

export interface UsePortVisitorsResult {
  visitors: FetchVisitorsResult;
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  refreshing: boolean;
  handleRefresh: () => void;
  handleLoadMore: () => void;
}
