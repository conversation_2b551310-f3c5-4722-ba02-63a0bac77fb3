import { useState, useEffect } from 'react';
import { showToast } from '@/src/utilities/toast';
import { fetchPortVisitors } from '@/src/networks/port/visitor';
import {
  FetchVisitorsResult,
  UsePortVisitorsParams,
  UsePortVisitorsResult,
  FetchVisitorsParams,
} from './types';

const usePortVisitors = ({
  unLocode,
  dataType,
  pageSize = 10,
}: UsePortVisitorsParams): UsePortVisitorsResult => {
  const [visitors, setVisitors] = useState<FetchVisitorsResult>({
    total: 0,
    data: [],
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(false);
  const [page, setPage] = useState<number>(0);

  const fetchVisitors = async (pageNumber: number, isRefresh = false): Promise<void> => {
    try {
      if (isRefresh) {
        setRefreshing(true);
        setPage(0);
      } else if (pageNumber > 0) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const params: FetchVisitorsParams = {
        unLocode,
        dataType,
        page: pageNumber,
        pageSize,
      };

      const result = await fetchPortVisitors({ ...params });

      if (isRefresh || pageNumber === 0) {
        setVisitors(result);
      } else {
        setVisitors((prev) => ({
          total: result.total,
          data: [...prev.data, ...result.data],
        }));
      }

      setPage(pageNumber);
      setHasMore(result.data.length === pageSize);
    } catch (err) {
      showToast({
        type: 'error',
        message: `Failed to load port visitors: ${
          err instanceof Error ? err.message : 'Unknown error'
        }`,
      });
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (unLocode && dataType) {
      fetchVisitors(0);
    }
  }, [unLocode, dataType]);

  const handleRefresh = (): void => {
    fetchVisitors(0, true);
  };

  const handleLoadMore = (): void => {
    if (!loadingMore && !loading && hasMore) {
      fetchVisitors(page + 1);
    }
  };

  return {
    visitors,
    loading,
    loadingMore,
    hasMore,
    refreshing,
    handleRefresh,
    handleLoadMore,
  };
};

export default usePortVisitors;
