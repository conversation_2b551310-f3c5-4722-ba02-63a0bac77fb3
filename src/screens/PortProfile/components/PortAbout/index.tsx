import React from 'react';
import { FlatList, Text, View } from 'react-native';
import { SectionHeader } from '@/src/components/SectionHeader';
import Education from '@/src/assets/svgs/Education';
import { PortAboutProps } from './types';

const PortAbout: React.FC<PortAboutProps> = ({ portData }) => {
  if (!portData) return null;

  const getPortLocalTime = (): string => {
    const tz = portData?.timezone;
    if (!tz?.timezone) return '-';
    try {
      return new Date().toLocaleTimeString('en-US', {
        timeZone: tz.timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      });
    } catch {
      return '-';
    }
  };

  const formatValue = (value: number | string | null | undefined): string =>
    value !== null && value !== undefined ? String(value) : '-';

  const portDetails = [
    { label: 'Country', value: formatValue(portData?.country?.name) },
    { label: 'City', value: formatValue(portData?.city?.name) },
    { label: 'UN/LOCODE', value: formatValue(portData?.unLocode) },
    {
      label: 'Latitude/Longitude',
      value: (() => {
        const lat = parseFloat(portData?.latitude ?? '');
        const lng = parseFloat(portData?.longitude ?? '');

        if (isNaN(lat) || isNaN(lng)) return '-';

        const convertToDMS = (coordinate: number, isLatitude: boolean) => {
          const absolute = Math.abs(coordinate);
          const degrees = Math.floor(absolute);
          const minutesNotTruncated = (absolute - degrees) * 60;
          const minutes = Math.floor(minutesNotTruncated);
          const seconds = Math.floor((minutesNotTruncated - minutes) * 60);

          const direction = isLatitude
            ? coordinate >= 0
              ? 'N'
              : 'S'
            : coordinate >= 0
              ? 'E'
              : 'W';

          return `${degrees}° ${minutes}' ${seconds}" ${direction}`;
        };

        const latDMS = convertToDMS(lat, true);
        const lngDMS = convertToDMS(lng, false);

        return `${latDMS}, ${lngDMS}`;
      })(),
    },
    { label: 'No. of terminals', value: formatValue(portData?.noOfTerminals) },
    { label: 'Max draught', value: formatValue(portData?.maxDraught) },
    { label: 'Max deadweight', value: formatValue(portData?.maxDeadweight) },
    { label: 'Max length', value: formatValue(portData?.maxLength) },
    { label: 'Max air draught', value: formatValue(portData?.maxAirDraught) },
    { label: 'Timezone', value: formatValue(portData?.timezone?.timezone) },
    { label: 'Local time', value: getPortLocalTime() },
  ];

  return (
    <FlatList
      data={portDetails}
      keyExtractor={(_, index) => index.toString()}
      ListHeaderComponent={
        <SectionHeader containerClassName="px-4" title="Port details" icon={Education} />
      }
      renderItem={({ item, index }) => (
        <View
          className={`flex-row justify-between px-4 py-3 ${
            index === portDetails.length - 1 ? '' : 'border-b border-borderGrayExtraLight'
          }`}
        >
          <Text className="text-gray-600 font-normal text-sm w-1/2">{item.label}</Text>
          <Text className="text-black font-normal text-sm text-left w-1/2">{item.value}</Text>
        </View>
      )}
    />
  );
};

export default PortAbout;
