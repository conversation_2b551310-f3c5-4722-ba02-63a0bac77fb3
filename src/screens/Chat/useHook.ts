import { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import {
  findAllSpecificProfileChats,
  deleteSpecificProfileChat,
} from '@/src/networks/chat/individual';
import { useChatSocket } from '@/src/networks/chat/socket';
import type { MessageData } from '@/src/networks/chat/types';
import { fetchProfileAPI } from '@/src/networks/profile/userProfile';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import type { MediaPreviewItem, MessageI, ProfileData } from './types';

export const useChatScreenHook = (profileId: string) => {
  const [messages, setMessages] = useState<MessageI[]>([]);
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalMessages, setTotalMessages] = useState(0);
  const [replyPreview, setReplyPreview] = useState<MessageI | null>(null);
  const [selectedMessage, setSelectedMessage] = useState<MessageI | null>(null);
  const [optionsVisible, setOptionsVisible] = useState(false);
  const [messageText, setMessageText] = useState('');
  const [sending, setSending] = useState(false);
  const [pendingMessages, setPendingMessages] = useState<Map<string, MessageI>>(new Map());
  const [isUserOnline, setIsUserOnline] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [deleteOptionsVisible, setDeleteOptionsVisible] = useState(false);
  const [clearChatOptions, setClearChatOptionsVisible] = useState(false);
  const [clearChatVisible, setClearChatVisible] = useState(false);
  const [lastSeen, setLastSeen] = useState<Date | null>(null);

  const currentUser = useSelector(selectCurrentUser);
  const flatListRef = useRef<any>(null);
  const initializationRef = useRef(false);
  const PAGE_SIZE = 10;

  const {
    connect,
    disconnect,
    sendMessage: socketSend,
    onMessage,
    removeMessageHandler,
    isConnected,
    isConnecting,
  } = useChatSocket(currentUser.profileId);

  const defaultUserProfile = {
    name: 'User',
    profileId,
    email: '',
    username: '',
    avatar: null,
    designation: null,
    entity: null,
  };

  const currentUserProfile = {
    name: 'You',
    profileId: currentUser.profileId,
    email: '',
    username: '',
    avatar: null,
    designation: null,
    entity: null,
  };

  const loadMessages = async (page = 0, isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (page > 0) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const response = await findAllSpecificProfileChats({
        profileId,
        page,
        pageSize: PAGE_SIZE,
      });

      const messagesWithUser: MessageI[] = response.data.map((msg: MessageData) => ({
        ...msg,
        user:
          msg.senderId === currentUser.profileId
            ? currentUserProfile
            : profile || defaultUserProfile,
      }));

      if (isRefresh) {
        setMessages(messagesWithUser);
        setCurrentPage(0);
      } else if (page > 0) {
        setMessages((prev) => [...prev, ...messagesWithUser]);
        setCurrentPage(page);
      } else {
        setMessages(messagesWithUser);
        setCurrentPage(0);
      }

      setTotalMessages(response.total);
      setHasMore(response.hasMore);
    } catch (err) {
      console.error('Failed to load messages:', err);
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  };

  const refreshMessages = () => {
    loadMessages(0, true);
  };

  const loadProfile = async () => {
    try {
      const profileData = await fetchProfileAPI(profileId);
      setProfile(profileData);
    } catch (err) {
      console.error('Failed to load profile:', err);
    }
  };

  const initializeSocket = async () => {
    if (initializationRef.current || isConnected || isConnecting) {
      return;
    }

    try {
      initializationRef.current = true;
      await connect();
    } catch (error) {
      console.error('[ChatHook] Failed to connect socket:', error);
      initializationRef.current = false;
    }
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore && !refreshing) {
      loadMessages(currentPage + 1);
    }
  };

  const uploadMedia = async (selectedMedia: any[]): Promise<MediaPreviewItem[]> => {
    const extensions = selectedMedia.map((file) => file.mime.split('/')[1]).filter((ext) => ext);

    if (extensions.length === 0) {
      throw new Error('No valid media files');
    }

    const response = await fetchPresignedUrlAPI(extensions, 'POST');

    if (!Array.isArray(response) || response.length !== selectedMedia.length) {
      throw new Error('Failed to get upload URLs');
    }

    await Promise.all(
      selectedMedia.map((file, index) => {
        const presignedData = response[index];
        const fileBlob = {
          uri: file.path,
          type: file.mime,
          filename: file.filename || `media_${index}.${file.mime.split('/')[1]}`,
        };
        return uploadFileWithPresignedUrl(fileBlob, presignedData.uploadUrl);
      }),
    );

    return response.map((item, index) => ({
      url: item.accessUrl,
      mimeType: selectedMedia[index].mime,
      name:
        selectedMedia[index].filename ||
        `media_${index}.${selectedMedia[index].mime.split('/')[1]}`,
    }));
  };

  const sendMessage = async () => {
    if (!messageText.trim() || sending || !isConnected) return;

    const tempId = `temp_${Date.now()}`;
    const currentMessageText = messageText.trim();
    const currentReplyTo = replyPreview?.id || null;

    try {
      setSending(true);

      const tempMessage: MessageI = {
        id: tempId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: { text: currentMessageText, media: [] },
        messageType: 'TEXT',
        replyTo: currentReplyTo,
        createdAt: new Date(),
        deletedForAll: false,
        deletedFor: [],
        user: currentUserProfile,
      };

      setMessages((prev) => [...prev, tempMessage]);
      setPendingMessages((prev) => new Map(prev.set(tempId, tempMessage)));
      setMessageText('');
      setReplyPreview(null);

      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);

      socketSend('individual', {
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: currentMessageText,
          media: [],
        },
        replyTo: currentReplyTo,
        profileId: currentUser.profileId,
      });
    } catch (err) {
      console.error('[ChatHook] Error sending message:', err);
      setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
      setPendingMessages((prev) => {
        const newMap = new Map(prev);
        newMap.delete(tempId);
        return newMap;
      });
    } finally {
      setSending(false);
    }
  };

  const sendMediaMessage = async (mediaItems: MediaPreviewItem[], captionText?: string) => {
    if (!isConnected || sending || mediaItems.length === 0) {
      return;
    }

    const tempId = `temp_${Date.now()}`;
    const currentReplyTo = replyPreview?.id || null;

    try {
      setSending(true);
      const tempMessage: MessageI = {
        id: tempId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: captionText || null,
          media: mediaItems,
        },
        messageType: 'MEDIA',
        replyTo: currentReplyTo,
        createdAt: new Date(),
        deletedForAll: false,
        deletedFor: [],
        user: currentUserProfile,
      };
      setMessages((prev) => [...prev, tempMessage]);
      setPendingMessages((prev) => new Map(prev.set(tempId, tempMessage)));
      if (replyPreview) {
        setReplyPreview(null);
      }
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
      const socketPayload = {
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: captionText || null,
          media: mediaItems.map((item) => {
            const mimeType = item.mimeType.toLowerCase();
            let normalizedMimeType = mimeType;

            if (mimeType.startsWith('image/')) {
              normalizedMimeType = 'JPEG';
            } else if (mimeType === 'application/pdf' || mimeType.includes('pdf')) {
              normalizedMimeType = 'PDF';
            } else if (mimeType === 'text/plain') {
              normalizedMimeType = 'TXT';
            }

            return {
              ...item,
              mimeType: normalizedMimeType,
            };
          }),
        },
        replyTo: currentReplyTo,
        profileId: currentUser.profileId,
      };
      socketSend('individual', socketPayload);
    } catch (err) {
      setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
      setPendingMessages((prev) => {
        const newMap = new Map(prev);
        newMap.delete(tempId);
        return newMap;
      });
    } finally {
      setSending(false);
    }
  };

  const editMessage = async (messageId: string, newContent: string) => {
    try {
      socketSend('edit-message', {
        id: messageId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: newContent,
          media: [],
        },
        profileId: currentUser.profileId,
      });

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId
            ? { ...msg, content: { ...msg.content, text: newContent }, editedAt: new Date() }
            : msg,
        ),
      );
    } catch (err) {
      console.error('[ChatHook] Error editing message:', err);
    }
  };

  const deleteManyForMe = async () => {
    const messageIds = Array.from(selectedMessages);
    if (messageIds.length === 0) return;
    try {
      socketSend('delete-for-me', {
        ids: messageIds,
        senderId: currentUser.profileId,
        recieverId: profileId,
        profileId: currentUser.profileId,
        type: 'FOR_ME',
      });

      setMessages((prev) => prev.filter((msg) => !messageIds.includes(msg.id)));
      setSelectedMessages(new Set());
      setIsSelectionMode(false);
    } catch (err) {
      console.error('[ChatHook] Error deleting messages for me:', err);
    }
  };

  const deleteManyForEveryone = async () => {
    const messageIds = Array.from(selectedMessages);
    if (messageIds.length === 0) return;
    try {
      socketSend('delete-for-everyone', {
        ids: messageIds,
        senderId: currentUser.profileId,
        recieverId: profileId,
        profileId: currentUser.profileId,
        type: 'FOR_EVERYONE',
      });

      setMessages((prev) =>
        prev.map((msg) =>
          messageIds.includes(msg.id)
            ? {
                ...msg,
                deletedForAll: true,
                content: { ...msg.content, text: 'This message has been deleted' },
              }
            : msg,
        ),
      );
      setSelectedMessages(new Set());
      setIsSelectionMode(false);
    } catch (err) {
      console.error('[ChatHook] Error deleting messages for everyone:', err);
    }
  };

  const clearChat = async () => {
    try {
      await deleteSpecificProfileChat(profileId);
      setMessages([]);
      setClearChatVisible(false);
    } catch (err) {
      console.error('[ChatHook] Error clearing chat:', err);
    }
  };

  const handleMessageLongPress = (message: MessageI) => {
    if (isSelectionMode) {
      toggleMessageSelection(message.id);
    } else {
      setSelectedMessage(message);
      setOptionsVisible(true);
    }
  };

  const handleMessagePress = (message: MessageI) => {
    if (isSelectionMode) {
      toggleMessageSelection(message.id);
    }
  };

  const toggleMessageSelection = (messageId: string) => {
    setSelectedMessages((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }

      if (newSet.size === 0) {
        setIsSelectionMode(false);
      }

      return newSet;
    });
  };

  const startSelectionMode = (messageId: string) => {
    setIsSelectionMode(true);
    setSelectedMessages(new Set([messageId]));
  };

  const exitSelectionMode = () => {
    setIsSelectionMode(false);
    setSelectedMessages(new Set());
  };

  const handleDeleteSelected = () => {
    if (selectedMessages.size > 0) {
      setDeleteOptionsVisible(true);
    }
  };

  const handleDeleteSelectedForMe = () => {
    const messageIds = Array.from(selectedMessages);
    if (messageIds.length === 0) return;
    deleteManyForMe();
    setDeleteOptionsVisible(false);
    setIsSelectionMode(false);
  };

  const handleDeleteSelectedForEveryone = () => {
    const messageIds = Array.from(selectedMessages);
    if (messageIds.length === 0) return;
    const canDeleteForEveryone = messageIds.every((id) => {
      const message = messages.find((msg) => msg.id === id);
      return (
        message && message.senderId === currentUser.profileId && canDeleteForEveryoneCheck(message)
      );
    });

    if (canDeleteForEveryone) {
      deleteManyForEveryone();
    }
    setDeleteOptionsVisible(false);
    setIsSelectionMode(false);
  };

  const handleClearChat = () => {
    setClearChatOptionsVisible(false);
  };

  const handleConfirmClearChat = () => {
    clearChat();
  };

  const canDeleteForEveryoneCheck = (message: MessageI): boolean => {
    if (message.senderId !== currentUser.profileId || message.deletedForAll) return false;
    const deleteTimeLimit = new Date(message.createdAt);
    deleteTimeLimit.setMinutes(deleteTimeLimit.getMinutes() + 30);
    return new Date() <= deleteTimeLimit;
  };

  const canDeleteSelectedForEveryone = (): boolean => {
    return Array.from(selectedMessages).every((id) => {
      const message = messages.find((msg) => msg.id === id);
      return (
        message && message.senderId === currentUser.profileId && canDeleteForEveryoneCheck(message)
      );
    });
  };

  const handleReply = () => {
    if (selectedMessage) {
      setReplyPreview(selectedMessage);
    }
    setOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleSwipeReply = (message: MessageI) => {
    setReplyPreview(message);
  };

  const handleCloseOptions = () => {
    setOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleCloseReply = () => {
    setReplyPreview(null);
  };

  const socketHandlers = {
    handleIndividualMessage: (data: any) => {
      const messageWithUser: MessageI = {
        ...data,
        user:
          data.senderId === currentUser.profileId
            ? currentUserProfile
            : profile || defaultUserProfile,
      };

      const tempMessage = Array.from(pendingMessages.values()).find(
        (msg) =>
          msg.senderId === data.senderId &&
          msg.recieverId === data.recieverId &&
          (msg.content.text === data.content.text || (msg.content.media && data.content.media)),
      );

      if (tempMessage) {
        setMessages((prev) =>
          prev.map((msg) => (msg.id === tempMessage.id ? messageWithUser : msg)),
        );
        setPendingMessages((prev) => {
          const newMap = new Map(prev);
          newMap.delete(tempMessage.id);
          return newMap;
        });
      } else {
        setMessages((prev) => [...prev, messageWithUser]);
      }

      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    },

    handleMessageError: (data: any) => {
      const failedMessage = Array.from(pendingMessages.values()).find(
        (msg) =>
          msg.senderId === data.originalSenderId && msg.recieverId === data.originalRecieverId,
      );

      if (failedMessage) {
        setMessages((prev) => prev.filter((msg) => msg.id !== failedMessage.id));
        setPendingMessages((prev) => {
          const newMap = new Map(prev);
          newMap.delete(failedMessage.id);
          return newMap;
        });
      }
    },

    handleMessagesDeleted: (data: { ids: string[]; senderId: string }) => {
      setMessages((prev) => prev.filter((msg) => !data.ids.includes(msg.id)));
    },

    handleMessageDeleted: (data: { id: string; senderId: string }) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === data.id
            ? {
                ...msg,
                deletedForAll: true,
                content: { ...msg.content, text: 'This message has been deleted' },
              }
            : msg,
        ),
      );
    },

    handleMessageEdited: (data: { id: string; content: any; senderId: string }) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === data.id ? { ...msg, content: data.content, editedAt: new Date() } : msg,
        ),
      );
    },

    handleUserStatus: (data: { profileId: string; status: string; lastSeen: string }) => {
      if (data.profileId !== profileId) return;
      setIsUserOnline(data.status === 'online');
      setLastSeen(data.status === 'offline' ? new Date(data.lastSeen) : null);
    },
  };

  useEffect(() => {
    const initializeChat = async () => {
      try {
        await Promise.all([loadProfile(), loadMessages(0), initializeSocket()]);
      } catch (error) {
        console.error('Failed to initialize chat:', error);
        setLoading(false);
      }
    };

    initializeChat();
  }, [profileId]);

  useEffect(() => {
    if (!isConnected) return;

    const handlers = socketHandlers;

    onMessage('individual', handlers.handleIndividualMessage);
    onMessage('message-error', handlers.handleMessageError);
    onMessage('messages-deleted', handlers.handleMessagesDeleted);
    onMessage('message-deleted', handlers.handleMessageDeleted);
    onMessage('message-edited', handlers.handleMessageEdited);
    onMessage('user-status', handlers.handleUserStatus);

    return () => {
      removeMessageHandler('individual');
      removeMessageHandler('message-error');
      removeMessageHandler('messages-deleted');
      removeMessageHandler('message-deleted');
      removeMessageHandler('message-edited');
      removeMessageHandler('messages-read');
      removeMessageHandler('read-receipt');
      removeMessageHandler('user-status');
    };
  }, [isConnected, pendingMessages, profile]);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return {
    messages,
    profile,
    loading,
    loadingMore,
    refreshing,
    hasMore,
    totalMessages,
    currentPage,
    replyPreview,
    selectedMessage,
    optionsVisible,
    messageText,
    sending,
    flatListRef,
    isConnected,
    isUserOnline,
    lastSeen,
    selectedMessages,
    isSelectionMode,
    deleteOptionsVisible,
    clearChatVisible,
    setMessageText,
    setSelectedMessage,
    setDeleteOptionsVisible,
    setClearChatVisible,
    sendMessage,
    sendMediaMessage,
    uploadMedia,
    editMessage,
    handleDeleteSelectedForEveryone,
    handleDeleteSelectedForMe,
    clearChat,
    handleLoadMore,
    refreshMessages,
    handleMessageLongPress,
    handleMessagePress,
    handleReply,
    handleSwipeReply,
    handleCloseOptions,
    handleCloseReply,
    startSelectionMode,
    exitSelectionMode,
    handleDeleteSelected,
    handleClearChat,
    handleConfirmClearChat,
    canDeleteSelectedForEveryone,
    setOptionsVisible,
    clearChatOptions,
    setClearChatOptionsVisible,
  };
};
