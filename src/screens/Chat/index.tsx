import { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  Pressable,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator,
  Image,
  FlatList,
  RefreshControl,
} from 'react-native';
import { type RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import ImagePicker from 'react-native-image-crop-picker';
import { RFPercentage } from 'react-native-responsive-fontsize';
import { pick, types } from '@react-native-documents/picker';
import { viewDocument } from '@react-native-documents/viewer';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import BottomSheet from '@/src/components/Bottomsheet/index';
import ImageViewer from '@/src/components/ImageViewer';
import CustomModal from '@/src/components/Modal';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import SafeArea from '@/src/components/SafeArea';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { formatMessageTime } from '@/src/utilities/datetime';
import type { HomeStackParamListI } from '@/src/navigation/types';
import Attachment from '@/src/assets/svgs/Attachment';
import Close from '@/src/assets/svgs/Close';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import Microphone from '@/src/assets/svgs/Microphone';
import Send from '@/src/assets/svgs/Send';
import Trash from '@/src/assets/svgs/TrashBin';
import EditMessageInput from './components/EditMessageInput';
import ReplyPreview from './components/ReplyPreview';
import SwipeableMessage from './components/SwipeableMessage';
import type { MediaPreviewItem, MessageI } from './types';
import { useChatScreenHook } from './useHook';

const ChatScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<HomeStackParamListI, 'Chat'>>();
  const profileId = route.params.id;
  const currentUser = useSelector(selectCurrentUser);

  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState('');
  const [mediaOptionsVisible, setMediaOptionsVisible] = useState(false);
  const [messageOptionsVisible, setMessageOptionsVisible] = useState(false);
  const [pendingAction, setPendingAction] = useState<'camera' | 'gallery' | 'file' | null>(null);
  const [mediaPreview, setMediaPreview] = useState<MediaPreviewItem[]>([]);
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [selectedImagePost, setSelectedImagePost] = useState<any>(null);

  const textInputRef = useRef<TextInput>(null);
  const editInputRef = useRef<TextInput>(null);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);

  const {
    messages,
    profile,
    loading,
    loadingMore,
    refreshing,
    replyPreview,
    selectedMessage,
    optionsVisible,
    messageText,
    sending,
    flatListRef,
    isUserOnline,
    lastSeen,
    selectedMessages,
    isSelectionMode,
    deleteOptionsVisible,
    clearChatVisible,
    setMessageText,
    setDeleteOptionsVisible,
    setClearChatVisible,
    sendMessage,
    sendMediaMessage,
    uploadMedia,
    editMessage,
    handleLoadMore,
    refreshMessages,
    handleMessageLongPress,
    handleMessagePress,
    handleReply,
    handleSwipeReply,
    handleCloseOptions,
    handleCloseReply,
    setOptionsVisible,
    setSelectedMessage,
    startSelectionMode,
    exitSelectionMode,
    handleDeleteSelected,
    handleDeleteSelectedForMe,
    handleDeleteSelectedForEveryone,
    handleClearChat,
    handleConfirmClearChat,
    canDeleteSelectedForEveryone,
    setClearChatOptionsVisible,
    clearChatOptions,
  } = useChatScreenHook(profileId);

  const onBack = () => {
    if (isSelectionMode) {
      exitSelectionMode();
    } else {
      navigation.goBack();
    }
  };

  const handleAttachment = () => {
    setMediaOptionsVisible(true);
  };

  const handleCameraPress = () => {
    setPendingAction('camera');
    setMediaOptionsVisible(false);
  };

  const handleGalleryPress = () => {
    setPendingAction('gallery');
    setMediaOptionsVisible(false);
  };

  const handleFilePress = () => {
    setPendingAction('file');
    setMediaOptionsVisible(false);
  };

  const handleModalHide = () => {
    if (!pendingAction) return;

    setTimeout(() => {
      if (pendingAction === 'camera') {
        openCamera();
      } else if (pendingAction === 'gallery') {
        openGallery();
      } else if (pendingAction === 'file') {
        openFilePicker();
      }
      setPendingAction(null);
    }, 300);
  };

  const openCamera = () => {
    ImagePicker.openCamera({
      width: 1024,
      height: 1024,
      cropping: false,
      mediaType: 'photo',
      includeBase64: false,
    })
      .then((image) => {
        handleMediaSelection([image]);
      })
      .catch((error) => {
        if (error.code !== 'E_PICKER_CANCELLED') {
          console.error('Camera error:', error);
        }
      });
  };

  const openGallery = () => {
    ImagePicker.openPicker({
      multiple: true,
      mediaType: 'photo',
      includeBase64: false,
      maxFiles: 5,
    })
      .then((images) => {
        handleMediaSelection(Array.isArray(images) ? images : [images]);
      })
      .catch((error) => {
        if (error.code !== 'E_PICKER_CANCELLED') {
          console.error('Gallery error:', error);
        }
      });
  };

  const openFilePicker = async () => {
    try {
      const files = await pick({
        allowMultiSelection: true,
        type: [types.images, types.pdf, types.plainText],
      });

      const allFilesAreSupported = files.every((file) => file.hasRequestedType);
      if (!allFilesAreSupported) {
        console.warn('Some selected files are not supported');
      }

      const formattedFiles = files.map((file) => ({
        path: file.uri,
        mime: file.type || 'application/octet-stream',
        filename: file.name,
        size: file.size,
      }));

      handleMediaSelection(formattedFiles);
    } catch (error: any) {
      if (error?.code !== 'DOCUMENT_PICKER_CANCELED') {
        console.error('File picker error:', error);
      }
    }
  };

  const handleMediaSelection = async (selectedMedia: any[]) => {
    const tempMediaItems: MediaPreviewItem[] = selectedMedia.map((item, index) => ({
      url: item.path,
      mimeType: item.mime,
      name: item.filename || `media_${index}.${item.mime.split('/')[1]}`,
      isUploading: true,
    }));

    setMediaPreview(tempMediaItems);

    try {
      const uploadedMedia = await uploadMedia(selectedMedia);
      setMediaPreview(uploadedMedia);
    } catch (error) {
      console.error('Failed to upload media:', error);
      setMediaPreview([]);
    }
  };

  const handleSendMessage = () => {
    if (mediaPreview.length > 0) {
      if (mediaPreview.some((item) => item.isUploading)) return;
      sendMediaMessage(mediaPreview, messageText.trim());
      setMessageText('');
      setMediaPreview([]);
    } else if (messageText.trim()) {
      sendMessage();
      setMessageText('');
    }
  };

  const handleCancelMediaPreview = () => {
    setMediaPreview([]);
  };

  const handleImagePress = (message: MessageI) => {
    if (message.content.media && message.content.media.length > 0) {
      const imagePost = {
        createdAt: message.createdAt,
        Profile: {
          avatar:
            message.senderId === currentUser.profileId ? currentUser.avatar : profile?.avatar || '',
          name:
            message.senderId === currentUser.profileId ? currentUser.fullName : profile?.name || '',
          designation:
            message.senderId === currentUser.profileId
              ? currentUser.designation
              : profile?.designation,
          entity:
            message.senderId === currentUser.profileId ? currentUser.organisation : profile?.entity,
        },
        Media: message.content.media.map((media) => ({
          fileUrl: media.url,
          caption: '',
        })),
      };
      setSelectedImagePost(imagePost);
      setImageViewerVisible(true);
    }
  };

  const handleDocumentPress = async (media: any) => {
    try {
      await viewDocument({
        uri: media.url,
        mimeType: media.mimeType,
      });
    } catch (error) {
      console.error('Error viewing document:', error);
    }
  };

  const getDateLabel = (timestamp: Date): string => {
    const messageDate = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const messageDateOnly = new Date(
      messageDate.getFullYear(),
      messageDate.getMonth(),
      messageDate.getDate(),
    );
    const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const yesterdayOnly = new Date(
      yesterday.getFullYear(),
      yesterday.getMonth(),
      yesterday.getDate(),
    );

    if (messageDateOnly.getTime() === todayOnly.getTime()) {
      return 'Today';
    } else if (messageDateOnly.getTime() === yesterdayOnly.getTime()) {
      return 'Yesterday';
    } else {
      return messageDate.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'short',
        day: 'numeric',
      });
    }
  };

  const canEditMessage = (message: MessageI): boolean => {
    if (message.senderId !== currentUser.profileId || message.deletedForAll) return false;
    const editTimeLimit = new Date(message.createdAt);
    editTimeLimit.setMinutes(editTimeLimit.getMinutes() + 15);
    return new Date() <= editTimeLimit;
  };

  const canDeleteForEveryone = (message: MessageI): boolean => {
    if (message.senderId !== currentUser.profileId || message.deletedForAll) return false;
    const deleteTimeLimit = new Date(message.createdAt);
    deleteTimeLimit.setMinutes(deleteTimeLimit.getMinutes() + 30);
    return new Date() <= deleteTimeLimit;
  };

  const handleMessageOptions = (message: MessageI) => {
    setSelectedMessage(message);
    setMessageOptionsVisible(true);
  };

  const handleMessageLongPressStart = (message: MessageI) => {
    longPressTimerRef.current = setTimeout(() => {
      if (!isSelectionMode) {
        startSelectionMode(message.id);
      }
    }, 500);
  };

  const handleMessageLongPressEnd = () => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
  };

  const handleMessageTap = (message: MessageI) => {
    if (isSelectionMode) {
      handleMessagePress(message);
    } else {
      handleMessageOptions(message);
    }
  };

  const handleEdit = () => {
    if (selectedMessage) {
      setEditingMessageId(selectedMessage.id);
      setEditingText(selectedMessage.content.text || '');
      setTimeout(() => {
        editInputRef.current?.focus();
      }, 100);
    }
    setMessageOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleDeleteMessage = () => {
    if (selectedMessage) {
      if (canDeleteForEveryone(selectedMessage)) {
        setDeleteOptionsVisible(true);
      } else {
        handleDeleteSelectedForMe();
      }
    }
    setMessageOptionsVisible(false);
  };

  const handleReplyToMessage = () => {
    if (selectedMessage && !selectedMessage.deletedForAll) {
      handleReply();
    }
    setMessageOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleSaveEdit = () => {
    if (editingMessageId && editingText.trim()) {
      editMessage(editingMessageId, editingText.trim());
      setEditingMessageId(null);
      setEditingText('');
    }
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditingText('');
  };

  const handleTextInputChange = (text: string) => {
    setMessageText(text);
  };

  const renderMediaDisplay = (mediaItems: any[], isMyMessage: boolean, message: MessageI) => {
    if (!mediaItems || mediaItems.length === 0) return null;

    const firstMedia = mediaItems[0];
    const mediaCount = mediaItems.length;

    const isImage = firstMedia.mimeType.startsWith('image/') || firstMedia.mimeType === 'JPEG';
    const isPdf = firstMedia.mimeType === 'application/pdf';
    const isText = firstMedia.mimeType === 'text/plain';

    return (
      <Pressable
        onPress={() => {
          if (isSelectionMode) {
            handleMessagePress(message);
          } else if (isImage) {
            handleImagePress(message);
          } else if (isPdf || isText) {
            handleDocumentPress(firstMedia);
          }
        }}
        className="rounded-2xl overflow-hidden mb-2 relative"
        style={{ minWidth: 200, maxWidth: 280 }}
      >
        {isImage ? (
          <Image
            source={{ uri: firstMedia.url }}
            style={{ width: '100%', height: 200, minWidth: 200 }}
            resizeMode="cover"
          />
        ) : (
          <View className="w-full p-4 bg-gray-200 rounded-2xl" style={{ minHeight: 120 }}>
            <View className="items-center justify-center flex-1">
              <Text className="text-4xl mb-2">{isPdf ? '📄' : isText ? '📝' : '📎'}</Text>
              <Text
                className={`${isMyMessage ? 'text-gray-700' : 'text-gray-600'} text-center font-medium`}
              >
                {firstMedia.name || 'File'}
              </Text>
              <Text
                className={`${isMyMessage ? 'text-gray-600' : 'text-gray-500'} text-xs text-center mt-1`}
              >
                Tap to view
              </Text>
            </View>
          </View>
        )}
        {mediaCount > 1 && (
          <View className="absolute top-2 right-2 bg-black/70 rounded-full px-2 py-1">
            <Text className="text-white text-xs font-medium">1/{mediaCount}</Text>
          </View>
        )}
        {isSelectionMode && selectedMessages.has(message.id) && (
          <View className="absolute top-2 left-2 bg-green-500 rounded-full w-6 h-6 items-center justify-center">
            <Text className="text-white text-xs font-bold">✓</Text>
          </View>
        )}
      </Pressable>
    );
  };

  const renderMediaPreview = () => {
    if (mediaPreview.length === 0) return null;

    const firstMedia = mediaPreview[0];
    const mediaCount = mediaPreview.length;
    const isImage = firstMedia.mimeType.startsWith('image/') || firstMedia.mimeType === 'JPEG';
    const isPdf = firstMedia.mimeType === 'application/pdf';
    const isText = firstMedia.mimeType === 'text/plain';

    return (
      <View className="rounded-2xl overflow-hidden mb-2 relative">
        {isImage ? (
          <Image source={{ uri: firstMedia.url }} className="w-full h-48" resizeMode="cover" />
        ) : (
          <View className="w-full p-3 bg-gray-200 rounded-2xl h-48 justify-center items-center">
            <Text className="text-4xl mb-2">{isPdf ? '📄' : isText ? '📝' : '📎'}</Text>
            <Text className="text-gray-600 text-center font-medium">
              {firstMedia.name || 'File'}
            </Text>
          </View>
        )}
        {firstMedia.isUploading && (
          <View className="absolute inset-0 bg-black/50 justify-center items-center">
            <ActivityIndicator size="large" color="#ffffff" />
            <Text className="text-white mt-2">Uploading...</Text>
          </View>
        )}
        {mediaCount > 1 && (
          <View className="absolute top-2 left-2 bg-black/70 rounded-full px-2 py-1">
            <Text className="text-white text-xs font-medium">1/{mediaCount}</Text>
          </View>
        )}
        <Pressable
          className="absolute top-2 right-2 bg-black/50 rounded-full p-1"
          onPress={handleCancelMediaPreview}
        >
          <Close color="#ffffff" width={2} height={2} />
        </Pressable>
      </View>
    );
  };

  const renderMessage = ({ item: msg, index }: { item: MessageI; index: number }) => {
    const time = formatMessageTime(new Date(msg.createdAt));
    const isMyMessage = msg.senderId === currentUser.profileId;
    const replyToMessage = msg.replyTo ? messages.find((m) => m.id === msg.replyTo) : null;
    const isEditing = editingMessageId === msg.id;
    const hasMedia = msg.content.media && msg.content.media.length > 0;
    const hasText = msg.content.text && msg.content.text.trim();
    const isSelected = selectedMessages.has(msg.id);

    const prevMessage = index > 0 ? messages[index - 1] : null;
    const showDateLabel =
      !prevMessage || getDateLabel(msg.createdAt) !== getDateLabel(prevMessage.createdAt);

    return (
      <View>
        {showDateLabel && (
          <Text className="text-center text-gray-500 font-semibold my-3">
            {getDateLabel(msg.createdAt)}
          </Text>
        )}
        <View className="mb-2 px-4">
          <SwipeableMessage
            message={msg}
            onReply={handleSwipeReply}
            onLongPress={handleMessageLongPress}
          >
            <Pressable
              onPress={() => handleMessageTap(msg)}
              onPressIn={() => handleMessageLongPressStart(msg)}
              onPressOut={handleMessageLongPressEnd}
              onLongPress={() => {
                if (!isSelectionMode) {
                  startSelectionMode(msg.id);
                }
              }}
            >
              <View
                className={`max-w-[80%] p-3 rounded-xl ${
                  isMyMessage
                    ? 'bg-violet-100 border border-violet-100 self-end'
                    : 'bg-white border border-gray-100 self-start'
                } ${msg.deletedForAll ? 'opacity-60' : ''} ${isSelected ? 'bg-green-50 border-green-200' : ''}`}
              >
                {isSelectionMode && (
                  <View className="absolute -top-2 -left-2 bg-green-500 rounded-full w-6 h-6 items-center justify-center z-10">
                    <Text className="text-white text-xs font-bold">{isSelected ? '✓' : ''}</Text>
                  </View>
                )}

                {replyToMessage && (
                  <View className="mb-2 p-2 bg-[#F5F3FF] border-l-2 border-[#D2C9FF]">
                    <Text className="text-xs font-medium text-gray-600">
                      {replyToMessage.senderId === currentUser.profileId
                        ? 'You'
                        : profile?.name || 'User'}
                    </Text>
                    <Text className="text-xs text-gray-700" numberOfLines={1}>
                      {replyToMessage.deletedForAll
                        ? 'This message has been deleted'
                        : replyToMessage.content.text || 'Media message'}
                    </Text>
                  </View>
                )}

                {isEditing ? (
                  <EditMessageInput
                    ref={editInputRef}
                    value={editingText}
                    onChangeText={setEditingText}
                    onSave={handleSaveEdit}
                    onCancel={handleCancelEdit}
                  />
                ) : (
                  <>
                    {hasMedia && renderMediaDisplay(msg.content.media as any, isMyMessage, msg)}

                    {hasText && (
                      <Text className="text-sm text-gray-900">
                        {msg.deletedForAll ? 'This message has been deleted' : msg.content.text}
                        {msg.editedAt && (
                          <Text className="text-xs text-gray-400 ml-1"> (edited)</Text>
                        )}
                      </Text>
                    )}
                  </>
                )}

                <Text className="text-xs text-gray-400 mt-1 text-right">{time}</Text>
              </View>
            </Pressable>
          </SwipeableMessage>
        </View>
      </View>
    );
  };

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View className="py-4">
        <ActivityIndicator size="small" color="#448600" />
      </View>
    );
  };

  const getBottomSheetHeight = (type: 'media' | 'delete' | 'chat' | 'confirm' | 'message') => {
    const baseHeight = RFPercentage(7);
    const itemHeight = RFPercentage(4);
    const padding = RFPercentage(4);

    switch (type) {
      case 'media':
        return baseHeight + itemHeight * 3 + padding;
      case 'delete':
        const deleteItems = canDeleteSelectedForEveryone() ? 2 : 1;
        return baseHeight + itemHeight * deleteItems + padding;
      case 'chat':
        return baseHeight + itemHeight + padding;
      case 'message':
        let messageOptions = 2;
        if (selectedMessage && canEditMessage(selectedMessage)) messageOptions++;
        if (selectedMessage && selectedMessage.senderId === currentUser.profileId) messageOptions++;
        return baseHeight + itemHeight * messageOptions;
      case 'confirm':
        return RFPercentage(30);
      default:
        return RFPercentage(25);
    }
  };

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center bg-white">
          <ActivityIndicator size="small" color="#448600" />
          <Text className="text-gray-600 mt-3 text-base">Loading chat...</Text>
        </View>
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <KeyboardAvoidingView
        className="flex-1 bg-white"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <View className="flex-row items-center gap-2 px-2 border-b border-gray-200 bg-white z-10">
          <BackButton label="" onBack={onBack} />
          {isSelectionMode ? (
            <View className="flex-1 flex-row items-center justify-between">
              <Text className="text-base font-medium">{selectedMessages.size} selected</Text>
              <Pressable
                onPress={handleDeleteSelected}
                className="p-2 rounded-full"
                disabled={selectedMessages.size === 0}
              >
                <Trash color="#ef4444" width={2} height={2} />
              </Pressable>
            </View>
          ) : (
            <View className="flex-1 flex-row items-center justify-between">
              <View className="flex-row items-center gap-2">
                <UserAvatar
                  avatarUri={profile?.avatar || ''}
                  name={profile?.name || ''}
                  width={37}
                  height={37}
                />
                <View>
                  <Text className="text-base font-medium">{profile?.name || 'User'}</Text>
                  <Text className="text-xs text-gray-500">
                    {isUserOnline ? 'Online' : lastSeen ? `Last seen ${lastSeen}` : 'Offline'}
                  </Text>
                </View>
              </View>
              <Pressable onPress={() => setClearChatOptionsVisible(true)} className="p-2">
                <HorizontalEllipsis color="#666666" width={4} height={4} />
              </Pressable>
            </View>
          )}
        </View>

        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ paddingVertical: 12 }}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={refreshMessages}
              tintColor="#448600"
            />
          }
          maintainVisibleContentPosition={{
            minIndexForVisible: 0,
            autoscrollToTopThreshold: 10,
          }}
        />

        {!isSelectionMode && (
          <View className="border-t border-gray-200 bg-white py-3">
            {replyPreview && <ReplyPreview message={replyPreview} onClose={handleCloseReply} />}

            {mediaPreview.length > 0 && (
              <View className="px-4 pt-3">
                {renderMediaPreview()}
                <Text className="text-sm text-gray-500 mb-2">Add a caption (optional)</Text>
              </View>
            )}

            <View className="flex-row items-end px-4 gap-3">
              <View className="flex-1 relative border rounded-full bg-[#F3ECEC] border-[#DEDEDE] pl-4 pr-14 py-3">
                <TextInput
                  ref={textInputRef}
                  value={messageText}
                  onChangeText={handleTextInputChange}
                  placeholder={
                    mediaPreview.length > 0
                      ? 'Add a caption...'
                      : replyPreview
                        ? 'Reply to message...'
                        : 'Type a message'
                  }
                  placeholderTextColor="#9CA3AF"
                  className="text-sm text-gray-900"
                  multiline
                  textAlignVertical="center"
                  editable={!sending}
                />
                <Pressable
                  onPress={handleSendMessage}
                  disabled={sending}
                  className="absolute right-1.5 bottom-[0.15rem] w-11 h-11 rounded-full items-center p-2 bg-green-800 justify-center"
                >
                  <Send color="#ffffff" width={2} height={2} />
                </Pressable>
              </View>
              <Pressable
                className="w-11 h-11 rounded-full items-center justify-center"
                onPress={handleAttachment}
                disabled={sending || mediaPreview.length > 0}
              >
                <Attachment color="#666666" width={4.5} height={4.5} />
              </Pressable>
              <Pressable className="w-11 h-11 rounded-full items-center justify-center">
                <Microphone color="#666666" width={3} height={3} />
              </Pressable>
            </View>
          </View>
        )}
      </KeyboardAvoidingView>

      <BottomSheet
        onModalHide={handleModalHide}
        height={getBottomSheetHeight('media')}
        visible={mediaOptionsVisible}
        onClose={() => setMediaOptionsVisible(false)}
      >
        <OptionsMenu>
          <OptionItem label="File" onPress={handleFilePress} />
          <View className="h-[1px] bg-gray-200" />
          <OptionItem label="Gallery" onPress={handleGalleryPress} />
          <View className="h-[1px] bg-gray-200" />
          <OptionItem label="Camera" onPress={handleCameraPress} />
        </OptionsMenu>
      </BottomSheet>
      {!selectedMessage?.deletedForAll && (
        <BottomSheet
          onModalHide={() => {}}
          height={getBottomSheetHeight('message')}
          visible={messageOptionsVisible}
          onClose={() => setMessageOptionsVisible(false)}
        >
          <OptionsMenu>
            <OptionItem label="Reply" onPress={handleReplyToMessage} />
            <View className="h-[1px] bg-gray-200" />
            {selectedMessage && canEditMessage(selectedMessage) && (
              <>
                <OptionItem label="Edit" onPress={handleEdit} />
                <View className="h-[1px] bg-gray-200" />
              </>
            )}
          </OptionsMenu>
        </BottomSheet>
      )}
      <BottomSheet
        onModalHide={() => {}}
        height={getBottomSheetHeight('delete')}
        visible={deleteOptionsVisible}
        onClose={() => setDeleteOptionsVisible(false)}
      >
        <OptionsMenu>
          <OptionItem label="Delete for me" onPress={handleDeleteSelectedForMe} />
          <View className="h-[1px] bg-gray-200" />
          {canDeleteSelectedForEveryone() && (
            <>
              <OptionItem label="Delete for everyone" onPress={handleDeleteSelectedForEveryone} />
              <View className="h-[1px] bg-gray-200" />
            </>
          )}
        </OptionsMenu>
      </BottomSheet>

      <BottomSheet
        onModalHide={() => setClearChatVisible(true)}
        height={getBottomSheetHeight('chat')}
        visible={clearChatOptions}
        onClose={() => setClearChatOptionsVisible(false)}
      >
        <OptionsMenu>
          <OptionItem label="Clear chat" onPress={handleClearChat} />
        </OptionsMenu>
      </BottomSheet>

      <CustomModal
        isVisible={clearChatVisible}
        onCancel={() => setClearChatVisible(false)}
        onConfirm={handleConfirmClearChat}
        title="Clear Chat"
        description="Are you sure you want to clear this chat? This action cannot be undone."
        confirmButtonVariant="danger"
        cancelText="Cancel"
        confirmText="Clear"
      />

      {selectedImagePost && (
        <ImageViewer
          isVisible={imageViewerVisible}
          onClose={() => {
            setImageViewerVisible(false);
            setSelectedImagePost(null);
          }}
          post={selectedImagePost}
        />
      )}
    </SafeArea>
  );
};

export default ChatScreen;
