import { forwardRef, useEffect } from 'react';
import { View, TextInput, Pressable, Text } from 'react-native';
import { EditMessageInputProps } from './types';

const EditMessageInput = forwardRef<TextInput, EditMessageInputProps>(
  ({ value, onChangeText, onSave, onCancel }, ref) => {
    useEffect(() => {
      if (ref && typeof ref === 'object' && ref.current) {
        ref.current.focus();
      }
    }, [ref]);

    return (
      <View className="gap-y-3">
        <TextInput
          ref={ref}
          value={value}
          onChangeText={onChangeText}
          className="text-base text-gray-900 leading-5 p-2 bg-white rounded-lg border border-gray-200"
          multiline
          autoFocus
          selectTextOnFocus
        />
        <View className="flex-row justify-end gap-2">
          <Pressable onPress={onCancel} className="px-4 py-2 rounded-lg bg-gray-100">
            <Text className="text-gray-700 font-medium">Cancel</Text>
          </Pressable>
          <Pressable
            onPress={onSave}
            className="px-4 py-2 rounded-lg bg-green-800"
            disabled={!value.trim()}
          >
            <Text className="text-white font-medium">Save</Text>
          </Pressable>
        </View>
      </View>
    );
  },
);

EditMessageInput.displayName = 'EditMessageInput';

export default EditMessageInput;
