import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { MessageData } from '@/src/networks/chat/types';

export interface MessageI extends MessageData {
  user: ProfileData;
}

export interface ProfileData {
  email: string;
  name: string;
  username: string;
  avatar: string | null;
  profileId: string;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
}

export interface MediaPreviewItem {
  url: string;
  mimeType: string;
  name?: string | null;
  isUploading?: boolean;
}
