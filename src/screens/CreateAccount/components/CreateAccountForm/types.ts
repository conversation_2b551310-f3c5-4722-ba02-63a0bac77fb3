/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { UseFormReturn } from 'react-hook-form';

export interface CreateAccountFormDataI {
  email: string;
  password: string;
  userName: string;
  confirmPassword: string;
}

export interface UseCreateAccountFormI {
  methods: UseFormReturn<CreateAccountFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: CreateAccountFormDataI) => Promise<void>;
  isUsernameAvailable: boolean;
}

export interface CreateAccountPropsI {
  onSignIn?: () => void;
}
