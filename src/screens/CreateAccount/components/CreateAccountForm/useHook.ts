/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { checkUsernameAPI } from '@/src/networks/profile/username';
import { CreateAccountFormDataI, UseCreateAccountFormI } from './types';

const useCreateAccount = (): UseCreateAccountFormI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUsernameAvailable, setIsUsernameAvailable] = useState(true);

  const methods = useForm<CreateAccountFormDataI>({
    mode: 'onChange',
    defaultValues: {
      userName: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const { watch, setError, clearErrors } = methods;
  const userName = watch('userName').trim();

  useEffect(() => {
    if (!userName) {
      setIsUsernameAvailable(true);
      clearErrors('userName');
      return;
    }

    const checkUsername = async () => {
      try {
        await checkUsernameAPI({ username: userName });
        setIsUsernameAvailable(true);
        clearErrors('userName');
      } catch (error) {
        handleError(error, {
          handle4xxError: () => {
            if (error instanceof APIResError && error.status === 429) {
              setIsUsernameAvailable(false);
              setError('userName', {
                message: 'Username is already taken',
              });
              return;
            }
            showToast({
              message: 'Username Check Failed',
              description: 'Unable to check username availability',
              type: 'error',
            });
          },
          handle5xxError: () => {
            showToast({
              message: 'Server Error',
              description: 'Please try again later',
              type: 'error',
            });
          },
        });
      }
    };

    const debounce = setTimeout(() => {
      checkUsername();
    }, 500);

    return () => clearTimeout(debounce);
  }, [userName, setError, clearErrors]);

  const onSubmit = async (_data: CreateAccountFormDataI) => {
    try {
      setIsSubmitting(true);
      showToast({
        type: 'info',
        message: 'Currently not in pipeline',
        description: 'Try google login for now',
      });
    } catch (_error) {
      showToast({
        type: 'error',
        message: 'Account Creation Failed',
        description: 'Please try again later',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    isUsernameAvailable,
  };
};

export default useCreateAccount;
