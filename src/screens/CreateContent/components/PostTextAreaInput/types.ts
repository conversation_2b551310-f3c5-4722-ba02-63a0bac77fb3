import type { RefObject } from 'react';
import type { TextInput } from 'react-native';

export interface PostTextAreaPropsI {
  textInputRef: RefObject<TextInput | null>;
  postText: string;
  editable: boolean;
  showCharacterCount: boolean;
  setPostText: (text: string) => void;
  showBorder?: boolean;
  maxLength?: number;
  aiPlaceholder?: string;
  isGenerating?: boolean;
  onStopGeneration?: () => void;
}
