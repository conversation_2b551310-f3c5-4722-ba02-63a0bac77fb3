import { View, Text, Pressable, Platform } from 'react-native';
import { twMerge } from 'tailwind-merge';
import TextInput from '@/src/components/TextInput';
import type { PostTextAreaPropsI } from './types';

const PostTextAreaInput = ({
  textInputRef,
  postText,
  setPostText,
  showBorder = true,
  maxLength,
  aiPlaceholder = "What's on your mind?",
  editable = true,
  showCharacterCount = false,
  isGenerating = false,
  onStopGeneration,
}: PostTextAreaPropsI) => {
  const currentLength = postText.length;
  const isNearLimit = maxLength ? currentLength > maxLength * 0.9 : false;

  return (
    <View
      className={twMerge(
        'flex-1 relative',
        showBorder ? 'border border-borderGrayLight rounded-lg overflow-hidden' : '',
      )}
    >
      <TextInput
        inputRef={textInputRef}
        className="text-base border-0 flex-1"
        placeholder={aiPlaceholder}
        placeholderTextColor="#9ca3af"
        multiline={true}
        value={postText}
        onChangeText={setPostText}
        textAlignVertical="top"
        autoFocus
        borderless
        maxLength={maxLength || 1000}
        editable={editable}
        blurOnSubmit={false}
        returnKeyType={Platform.OS === 'ios' ? 'default' : 'none'}
      />

      {isGenerating && onStopGeneration && (
        <View className="absolute top-3 right-3">
          <Pressable
            onPress={onStopGeneration}
            className="bg-red-500 px-3 py-1 rounded-full"
            hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
          >
            <Text className="text-white text-xs font-medium">Stop</Text>
          </Pressable>
        </View>
      )}

      {showCharacterCount && maxLength && (
        <View className="absolute bottom-1 right-4 bg-gray-100 px-2 py-1 rounded-full">
          <Text className={`text-xs ${isNearLimit ? 'text-orange-500' : 'text-gray-500'}`}>
            {currentLength}/{maxLength}
          </Text>
        </View>
      )}
    </View>
  );
};

export default PostTextAreaInput;
