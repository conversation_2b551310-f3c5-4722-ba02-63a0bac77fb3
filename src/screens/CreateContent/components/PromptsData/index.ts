const MARITIME_PROMPTS = [
  'Create a short post about marine engineering challenges and solutions',
  'Discuss the role of automation in modern ships',
  'Share knowledge about vessel maintenance and safety protocols',
  'Write about fuel efficiency in modern shipping',
  'Share knowledge about marine propulsion systems',
  'Write about ship design and construction',
  'Write about maritime cybersecurity challenges',
  'Write about engine room operations',
  'Write about ship energy efficiency measures',
  'Write about ship maintenance strategies',
  'Share knowledge about ship systems integration',
  'Discuss the challenges of remote monitoring',
  'Write about maritime quality assurance',
  'Discuss the evolution of maritime technology',
  'Write about maritime innovation and technology',
  'Share knowledge about ship automation systems',
  'Write about maritime logistics optimization',
];

export const getRandomPrompts = (count = 4): string[] => {
  const shuffled = [...MARITIME_PROMPTS].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};
