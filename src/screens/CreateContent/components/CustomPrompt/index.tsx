import { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  Pressable,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
} from 'react-native';
import Modal from 'react-native-modal';
import type { CustomPromptModalProps } from './types';

const CustomPromptModal = ({ visible, onClose, onSubmit }: CustomPromptModalProps) => {
  const [customPrompt, setCustomPrompt] = useState('');
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
      setKeyboardHeight(e.endCoordinates.height);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardHeight(0);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  const handleSubmit = () => {
    if (customPrompt.trim()) {
      onSubmit(customPrompt.trim());
      setCustomPrompt('');
      onClose();
    }
  };

  const handleClose = () => {
    setCustomPrompt('');
    onClose();
  };

  return (
    <Modal
      isVisible={visible}
      onBackdropPress={handleClose}
      onBackButtonPress={handleClose}
      animationIn="fadeIn"
      animationOut="fadeOut"
      backdropOpacity={0.5}
      animationInTiming={250}
      animationOutTiming={250}
      backdropTransitionInTiming={250}
      backdropTransitionOutTiming={1}
      statusBarTranslucent
      useNativeDriverForBackdrop
      hideModalContentWhileAnimating={false}
      avoidKeyboard
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <View
          className="justify-center items-center px-4"
          style={{ paddingBottom: Platform.OS === 'android' ? keyboardHeight : 0 }}
        >
          <View className="bg-white rounded-lg p-6 w-full max-w-sm">
            <Text className="text-lg font-semibold text-gray-900 mb-4">Custom AI Prompt</Text>

            <Text className="text-sm text-gray-600 mb-3">
              Describe what kind of post you want AI to generate:
            </Text>

            <TextInput
              value={customPrompt}
              onChangeText={setCustomPrompt}
              placeholder="e.g., Write about ship maintenance best practices..."
              placeholderTextColor="#9ca3af"
              className="border border-gray-300 rounded-lg p-3 text-base text-gray-900 mb-4"
              multiline
              numberOfLines={3}
              textAlignVertical="top"
              autoFocus
              style={{ minHeight: 80 }}
            />

            <View className="flex-row gap-3">
              <Pressable onPress={handleClose} className="flex-1 py-3 px-4 bg-gray-100 rounded-lg">
                <Text className="text-center text-gray-700 font-medium">Cancel</Text>
              </Pressable>

              <Pressable
                onPress={handleSubmit}
                className={`flex-1 py-3 px-4 rounded-lg ${customPrompt.trim() ? 'bg-violet-600' : 'bg-gray-300'}`}
                disabled={!customPrompt.trim()}
              >
                <Text
                  className={`text-center font-medium ${customPrompt.trim() ? 'text-white' : 'text-gray-500'}`}
                >
                  Generate
                </Text>
              </Pressable>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default CustomPromptModal;
