import { useEffect, useState, useRef } from 'react';
import {
  Pressable,
  Text,
  View,
  Image,
  TouchableWithoutFeedback,
  ActivityIndicator,
  ScrollView,
  InteractionManager,
  Platform,
  Keyboard,
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import Config from 'react-native-config';
import EventSource from 'react-native-sse';
import BottomSheet from '@/src/components/Bottomsheet';
import Button from '@/src/components/Button';
import RateLimitModal from '@/src/components/RateLimitModal';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import Attachment from '@/src/assets/svgs/Attachment';
import Close from '@/src/assets/svgs/Close';
import useStorage from '@/src/hooks/storage';
import CustomPromptModal from '../CustomPrompt';
import EnhanceTextModal from '../EnhanceTextModal';
import PostTextAreaInput from '../PostTextAreaInput';
import PostThumbnail from '../PostThumbnail';
import { getRandomPrompts } from '../PromptsData';
import type { CreatePostFormProps } from './types';
import useCreatePostForm from './useHook';

const AI_URL = Config.AI_URL;

const CreatePostForm = ({
  onSuccess,
  type,
  portUnLocode,
  editing,
  postId,
}: CreatePostFormProps) => {
  const {
    caption,
    setCaption,
    isSubmitting,
    isLoading,
    textInputRef,
    handlePost,
    handleAttachments,
    isCaptioning,
    media,
    handleDeletePost,
    handleDone,
    handlePostThumbnail,
    handlePreview,
    isDone,
    selectedImage,
    postText,
    setPostText,
    setMedia,
    setIsCaptioning,
  } = useCreatePostForm({ onSuccess, type, portUnLocode, editing, postId });

  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [showEnhanceModal, setShowEnhanceModal] = useState(false);
  const [showAIBottomSheet, setShowAIBottomSheet] = useState(false);
  const [randomPrompts, setRandomPrompts] = useState<string[]>([]);
  const [aiMode, setAiMode] = useState<'global' | 'image'>('global');
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [aiText, setAiText] = useState('');
  const [streamedText, setStreamedText] = useState('');
  const [showRateLimitModal, setShowRateLimitModal] = useState(false);

  const { getStorage } = useStorage();
  const eventSourceRef = useRef<EventSource | null>(null);
  const streamingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const navigation = useNavigation<BottomTabNavigationI>();
  useEffect(() => {
    if (!aiText || isGenerating) {
      return;
    }

    let currentIndex = 0;
    setStreamedText('');

    const streamText = () => {
      if (currentIndex < aiText.length) {
        currentIndex = Math.min(currentIndex + 4, aiText.length);
        setStreamedText(aiText.slice(0, currentIndex));

        if (currentIndex < aiText.length) {
          streamingTimeoutRef.current = setTimeout(streamText, 50);
        } else {
          if (aiMode === 'global') {
            const maxLength = type === 'USER_POST' ? 2000 : 1000;
            const splicedText = aiText.slice(0, maxLength);
            setCaption(splicedText);
          } else {
            const splicedText = aiText.slice(0, 255);
            setPostText(splicedText);
          }
          setStreamedText('');
          setAiText('');
        }
      }
    };

    streamText();

    return () => {
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
    };
  }, [aiText, isGenerating, aiMode, type, setCaption, setPostText]);

  const generateContent = async (prompt: string, mode: 'global' | 'image') => {
    setIsGenerating(true);
    setAiText('');
    setStreamedText('');

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (streamingTimeoutRef.current) {
      clearTimeout(streamingTimeoutRef.current);
      streamingTimeoutRef.current = null;
    }

    const maxChars = mode === 'image' ? 150 : type === 'USER_POST' ? 1500 : 800;
    const promptType = mode === 'image' ? 'image caption' : 'social media post';

    const url = `${AI_URL}/query/general?query=${encodeURIComponent(
      `Generate a concise ${promptType} of max ${maxChars} characters or less, not more than that. Include relevant hashtags if appropriate. The content should be maritime-focused and professional, Just return the post text content.\n\nPrompt: ${prompt}`,
    )}&device=${await getStorage('deviceToken')}`;

    const es = new EventSource(url);
    eventSourceRef.current = es;

    let accumulatedText = '';

    es.addEventListener('open', () => {
      console.log('✅ EventSource connection opened successfully');
    });

    es.addEventListener('message', (event) => {
      try {
        const parsed = JSON.parse(event.data ?? '');
        const text = parsed.text;

        if (text === '[DONE]') {
          es.close();
          eventSourceRef.current = null;
          setIsGenerating(false);
          if (accumulatedText.trim()) {
            setAiText(accumulatedText);
          }
          return;
        }

        accumulatedText += text;
      } catch (err) {
        console.error('❌ Failed to parse SSE JSON:', event.data);
      }
    });

    es.addEventListener('error', (event) => {
      es.close();
      eventSourceRef.current = null;
      setIsGenerating(false);
      setAiText('');
      setStreamedText('');
      setShowRateLimitModal(true);
    });

    try {
      const response = await fetch(url, { method: 'HEAD' });
      if (response.status === 429) {
        es.close();
        eventSourceRef.current = null;
        setIsGenerating(false);
        setShowRateLimitModal(true);
        return;
      }
    } catch (error: unknown) {
      const fetchError = error as { status?: number };
      if (fetchError && fetchError.status === 429) {
        es.close();
        eventSourceRef.current = null;
        setIsGenerating(false);
        setShowRateLimitModal(true);
        return;
      }
    }
  };

  const stopGeneration = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (streamingTimeoutRef.current) {
      clearTimeout(streamingTimeoutRef.current);
      streamingTimeoutRef.current = null;
    }

    setIsGenerating(false);
    setAiText('');
    setStreamedText('');
  };

  const focusTextInput = () => {
    if (textInputRef.current && !isLoading) {
      if (Platform.OS === 'android') {
        InteractionManager.runAfterInteractions(() => {
          setTimeout(() => {
            textInputRef.current?.focus();
          }, 100);
        });
      } else {
        requestAnimationFrame(() => {
          textInputRef.current?.focus();
        });
      }
    }
  };

  const resetForm = () => {
    if (textInputRef.current) {
      textInputRef.current.clear();
    }
    setCaption('');
    setPostText('');
    setMedia([]);
    setIsCaptioning(false);
  };

  const handleClose = () => {
    resetForm();
    navigation.goBack();
  };

  const handleImageCaptionChange = (text: string) => {
    if (text.length <= 255) {
      setPostText(text);
    }
  };

  const handleGlobalCaptionChange = (text: string) => {
    if (text.length <= (type === 'USER_POST' ? 2000 : 1000)) {
      setCaption(text);
    }
  };

  const handleShowAIOptions = (mode: 'global' | 'image') => {
    setAiMode(mode);
    setRandomPrompts(getRandomPrompts(6));
    setShowAIBottomSheet(true);
  };

  const handleRefreshPrompts = () => {
    setRandomPrompts(getRandomPrompts(6));
  };

  const handlePromptSelect = (prompt: string) => {
    generateContent(prompt, aiMode);
    setShowAIBottomSheet(false);
  };

  const handleCustomPrompt = (prompt: string) => {
    generateContent(prompt, aiMode);
    setShowAIBottomSheet(false);
  };

  const handleEnhanceText = (prompt: string) => {
    generateContent(prompt, aiMode);
  };

  const handleShowEnhanceModal = () => {
    setAiMode(isCaptioning ? 'image' : 'global');
    setShowEnhanceModal(true);
  };

  const handleContainerPress = () => {
    if (!showAIBottomSheet && !showCustomPrompt && !showEnhanceModal && !showRateLimitModal) {
      focusTextInput();
    }
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setIsKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setIsKeyboardVisible(false);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  useFocusEffect(() => {
    const timer = setTimeout(
      () => {
        focusTextInput();
      },
      Platform.OS === 'android' ? 300 : 100,
    );

    return () => clearTimeout(timer);
  });

  useEffect(() => {
    const unsubscribe = navigation.addListener('blur', () => {
      resetForm();
    });

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
    };
  }, []);

  if (isLoading) {
    return (
      <View className="flex-1 bg-white justify-center items-center">
        <ActivityIndicator size="small" color="#10B981" />
        <Text className="mt-2 text-gray-600">Loading post...</Text>
      </View>
    );
  }

  const maxLength = type === 'USER_POST' ? 2000 : 1000;
  const hasGlobalText = caption.trim().length > 0;
  const hasImageText = postText.trim().length > 0;
  const currentText = isCaptioning ? postText : caption;
  const isStreaming = streamedText.length > 0;

  return (
    <TouchableWithoutFeedback onPress={handleContainerPress}>
      <View className="flex-1 bg-white">
        <View className="flex-row justify-between items-center pr-4 border-b border-gray-100">
          <View className="flex-row items-center px-4 py-3">
            <Pressable
              onPress={handleClose}
              className="p-1.5 rounded-full active:bg-gray-100"
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Close height={2.5} width={2.5} />
            </Pressable>
            <Text className="text-xl font-semibold ml-3 text-gray-900">
              {type === 'USER_POST'
                ? editing
                  ? 'Edit Post'
                  : 'Create Post'
                : editing
                  ? 'Edit Scrapbook Note'
                  : 'Create Scrapbook Note'}
            </Text>
          </View>
          <View className="flex-row gap-3 items-center">
            {type === 'USER_POST' && (
              <Pressable style={{ opacity: 0.5 }} onPress={handleAttachments}>
                <Attachment />
              </Pressable>
            )}
            {isCaptioning ? (
              <Pressable onPress={handleDone}>
                <Text className="text-primaryGreen font-medium">Done</Text>
              </Pressable>
            ) : (
              <Button
                className="w-auto px-6 rounded-full"
                label={editing ? 'Update' : 'Post'}
                onPress={handlePost}
                loading={isSubmitting}
                disabled={isSubmitting || !caption.trim()}
              />
            )}
          </View>
        </View>

        <View className="flex-1">
          {!isCaptioning && (
            <View className="flex-1 relative">
              <PostTextAreaInput
                postText={
                  (isGenerating || isStreaming) && aiMode === 'global' ? streamedText : caption
                }
                setPostText={handleGlobalCaptionChange}
                textInputRef={textInputRef}
                showBorder={false}
                maxLength={maxLength}
                aiPlaceholder={
                  isGenerating
                    ? 'AI is thinking...'
                    : isStreaming
                      ? 'AI is writing...'
                      : "What's on your mind?"
                }
                editable={!isGenerating && !isStreaming}
                showCharacterCount={true}
                isGenerating={(isGenerating || isStreaming) && aiMode === 'global'}
                onStopGeneration={stopGeneration}
              />

              {media.length > 0 && selectedImage && (
                <View className="flex gap-4 p-3">
                  {isDone && !isCaptioning && (
                    <>
                      {media.map((post, index) => (
                        <Pressable
                          key={index}
                          onPress={() => handlePreview(post)}
                          className="flex-row flex-wrap gap-2 w-full"
                        >
                          <Image
                            source={{ uri: post.uri }}
                            width={143}
                            height={150}
                            className="rounded-md"
                          />
                          <Text className="leading-4 text-sm text-labelBlack w-1/2 line-clamp-[10]">
                            {post.caption}
                          </Text>
                        </Pressable>
                      ))}
                    </>
                  )}
                </View>
              )}

              {!isGenerating && !isStreaming && (
                <View
                  className={`border-t border-gray-100 mx-3 py-2 ${!isKeyboardVisible ? 'mb-5 mt-1' : 'mb-0'}`}
                >
                  <View className="flex-row gap-2">
                    <Pressable
                      onPress={() => handleShowAIOptions('global')}
                      className="flex-1 flex-row items-center justify-center py-2 px-4 bg-violet-50 rounded-lg border border-violet-200"
                      hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                    >
                      <Text className="text-violet-600 font-medium text-sm">⚓ Generate AI</Text>
                    </Pressable>

                    {hasGlobalText && (
                      <Pressable
                        onPress={handleShowEnhanceModal}
                        className="flex-1 flex-row items-center justify-center py-2 px-4 bg-blue-50 rounded-lg border border-blue-200"
                        hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                      >
                        <Text className="text-blue-600 font-medium text-sm">✨ Enhance Text</Text>
                      </Pressable>
                    )}
                  </View>
                </View>
              )}

              {(isGenerating || isStreaming) && aiMode === 'global' && (
                <View className="px-3 pb-2">
                  <Text className="text-xs text-gray-500">
                    {isGenerating
                      ? 'AI is thinking about maritime content...'
                      : 'AI is writing your post...'}
                  </Text>
                </View>
              )}
            </View>
          )}

          {isCaptioning && (
            <View className="flex-1 mx-3 relative">
              <View className="items-center justify-center py-4">
                <Image
                  source={{ uri: selectedImage! }}
                  style={{ width: 300, height: 200 }}
                  resizeMode="contain"
                  className="rounded-md"
                />
                <View className="flex-row flex-wrap justify-center gap-2 mt-4">
                  {media.map((post, index) => (
                    <PostThumbnail
                      key={index}
                      uri={post.uri}
                      isSelected={post.uri === selectedImage}
                      onPress={handlePostThumbnail}
                      handleTrash={handleDeletePost}
                    />
                  ))}
                </View>
              </View>

              <PostTextAreaInput
                postText={
                  (isGenerating || isStreaming) && aiMode === 'image' ? streamedText : postText
                }
                setPostText={handleImageCaptionChange}
                maxLength={255}
                textInputRef={textInputRef}
                aiPlaceholder={
                  isGenerating
                    ? 'AI is thinking...'
                    : isStreaming
                      ? 'AI is writing...'
                      : 'Describe this image'
                }
                editable={!isGenerating && !isStreaming}
                showCharacterCount={true}
                isGenerating={(isGenerating || isStreaming) && aiMode === 'image'}
                onStopGeneration={stopGeneration}
              />

              {!isGenerating && !isStreaming && (
                <View className={`p-3 ${!isKeyboardVisible ? 'mb-6' : ''}`}>
                  <View className="flex-row gap-2">
                    <Pressable
                      onPress={() => handleShowAIOptions('image')}
                      className="flex-1 flex-row items-center justify-center py-2 px-4 bg-violet-50 rounded-lg border border-violet-200"
                      hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                    >
                      <Text className="text-violet-600 font-medium text-sm">⚓ Generate AI</Text>
                    </Pressable>

                    {hasImageText && (
                      <Pressable
                        onPress={handleShowEnhanceModal}
                        className="flex-1 flex-row items-center justify-center py-2 px-4 bg-blue-50 rounded-lg border border-blue-200"
                        hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                      >
                        <Text className="text-blue-600 font-medium text-sm">✨ Enhance Text</Text>
                      </Pressable>
                    )}
                  </View>
                </View>
              )}

              {(isGenerating || isStreaming) && aiMode === 'image' && (
                <View className="px-3 pb-2">
                  <Text className="text-xs text-gray-500">
                    {isGenerating
                      ? 'AI is analyzing the image...'
                      : 'AI is writing your caption...'}
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>

        <BottomSheet
          visible={showAIBottomSheet}
          onClose={() => setShowAIBottomSheet(false)}
          onModalHide={() => {}}
          height={500}
        >
          <View className="flex-1 p-4">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-lg font-semibold text-gray-900">Generate with AI</Text>
              <Pressable
                onPress={() => setShowAIBottomSheet(false)}
                className="p-2 rounded-full active:bg-gray-100"
                hitSlop={8}
              >
                <Text className="text-gray-500 text-lg">✕</Text>
              </Pressable>
            </View>

            <View className="flex-row justify-between items-center mb-3">
              <Text className="text-gray-600 text-sm font-medium">Choose a maritime prompt:</Text>
              <Pressable
                onPress={handleRefreshPrompts}
                className="p-2 rounded-full active:bg-gray-100"
                hitSlop={8}
              >
                <Text className="text-violet-600 text-sm">🔄 Refresh</Text>
              </Pressable>
            </View>
            <ScrollView className="flex-1 gap-y-2 mb-4" showsVerticalScrollIndicator={false}>
              {randomPrompts.map((prompt, index) => (
                <Pressable
                  key={index}
                  onPress={() => handlePromptSelect(prompt)}
                  className="p-3 bg-gray-50 rounded-lg mt-2 border border-gray-200 active:bg-gray-100"
                >
                  <Text className="text-gray-700 text-sm">{prompt}</Text>
                </Pressable>
              ))}
            </ScrollView>

            <View className="flex-row gap-2 pb-3">
              <Pressable
                onPress={() => {
                  setShowAIBottomSheet(false);
                  setTimeout(() => setShowCustomPrompt(true), 1000);
                }}
                className="flex-1 p-3 bg-blue-50 rounded-lg border border-blue-200 active:bg-blue-100"
              >
                <Text className="text-blue-600 text-sm font-medium text-center">
                  ✏️ Custom Prompt
                </Text>
              </Pressable>

              <Pressable
                onPress={() => {
                  const surprisePrompt =
                    'Write a creative and engaging marine engineering, marine law etc related social media post';
                  handlePromptSelect(surprisePrompt);
                }}
                className="flex-1 p-3 bg-violet-50 rounded-lg border border-violet-200 active:bg-violet-100"
              >
                <Text className="text-violet-600 text-sm font-medium text-center">
                  🎲 Surprise me!
                </Text>
              </Pressable>
            </View>
          </View>
        </BottomSheet>

        <CustomPromptModal
          visible={showCustomPrompt}
          onClose={() => setShowCustomPrompt(false)}
          onSubmit={handleCustomPrompt}
        />

        <EnhanceTextModal
          visible={showEnhanceModal}
          onClose={() => setShowEnhanceModal(false)}
          onSubmit={handleEnhanceText}
          currentText={currentText}
        />

        <RateLimitModal visible={showRateLimitModal} onClose={() => setShowRateLimitModal(false)} />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default CreatePostForm;
