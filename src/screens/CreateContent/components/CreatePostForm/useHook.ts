import { useCallback, useRef, useState } from 'react';
import type { TextInput } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import ImagePicker from 'react-native-image-crop-picker';
import { useDispatch } from 'react-redux';
import {
  createPost,
  createScrapbookPost,
  editPost,
  editScrapbookPost,
} from '@/src/redux/slices/content/contentSlice';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { compressToTargetSize } from '@/src/utilities/upload/compress';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { fetchPostAPI } from '@/src/networks/content/post';
import type { PostMediaI } from '@/src/networks/content/types';
import { fetchScrapbookPost } from '@/src/networks/port/scrapbook';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import type { CreatePostFormProps, MediaI } from './types';

const useCreatePostForm = ({
  onSuccess,
  portUnLocode,
  type,
  editing,
  postId,
}: CreatePostFormProps) => {
  const [caption, setCaption] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [media, setMedia] = useState<MediaI[]>([]);
  const [originalMedia, setOriginalMedia] = useState<MediaI[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isCaptioning, setIsCaptioning] = useState(false);
  const [isDone, setIsDone] = useState(false);
  const [postText, setPostText] = useState('');

  const textInputRef = useRef<TextInput>(null);
  const dispatch = useDispatch<AppDispatch>();

  const fetchPostData = async () => {
    if (!postId) return;
    try {
      setIsLoading(true);
      if (type === 'SCRAPBOOK_POST') {
        const scrapbookPostData = await fetchScrapbookPost(postId);
        setCaption(scrapbookPostData.textPreview || '');
      } else {
        const postData = await fetchPostAPI(postId);
        setCaption(postData.caption || '');
        if (postData.Media && postData.Media.length > 0) {
          const mediaItems: MediaI[] = postData.Media.map((mediaItem, index) => ({
            uri: mediaItem.fileUrl,
            type: `image/${mediaItem.extension}`,
            filename: `media-${index}.${mediaItem.extension}`,
            caption: mediaItem.caption || undefined,
          }));

          setMedia(mediaItems);
          setOriginalMedia(mediaItems);
          setSelectedImage(mediaItems[0]?.uri || null);
          setIsDone(true);
        }
      }
    } catch (error) {
      showToast({
        message: 'Error',
        description: 'Failed to fetch post data',
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (editing && postId) {
        fetchPostData();
      }
    }, [editing, postId]),
  );

  const handleAttachments = async () => {
    try {
      const remainingSlots = 7 - media.length;
      if (remainingSlots <= 0) {
        showToast({
          message: 'Limit reached',
          description: 'You can only upload up to 7 media files.',
          type: 'info',
        });
        return;
      }

      const images = await ImagePicker.openPicker({
        multiple: true,
        mediaType: 'photo',
        maxFiles: remainingSlots,
      });

      if (!images || !Array.isArray(images)) return;

      const limitedImages = images.slice(0, remainingSlots);

      const compressedFiles: MediaI[] = await Promise.all(
        limitedImages.map(async (img, i) => {
          const compressedUri = await compressToTargetSize(img.path, 500);
          return {
            uri: compressedUri,
            type: 'image/jpeg',
            filename: img.filename ?? `File-${i}.jpg`,
          };
        }),
      );

      setMedia((prev) => [...prev, ...compressedFiles]);
      setSelectedImage(compressedFiles[0]?.uri ?? null);
      setPostText('');
      setIsCaptioning(true);
      setIsDone(false);
    } catch (error: unknown) {
      if (
        typeof error === 'object' &&
        error !== null &&
        'message' in error &&
        typeof (error as { message: unknown }).message === 'string' &&
        (error as { message: string }).message.toLowerCase().includes('cancel')
      ) {
        return;
      }
      showToast({
        message: 'Error',
        description: 'Media selection failed',
        type: 'error',
      });
    }
  };

  const handleDone = () => {
    if (selectedImage) {
      setMedia((prev) =>
        prev.map((item) => (item.uri === selectedImage ? { ...item, caption: postText } : item)),
      );
    }
    setIsCaptioning(false);
    setIsDone(true);
    setPostText('');
  };

  const handlePostThumbnail = (url: string) => {
    if (selectedImage && postText) {
      setMedia((prev) =>
        prev.map((item) => (item.uri === selectedImage ? { ...item, caption: postText } : item)),
      );
    }

    const existing = media.find((item) => item.uri === url);
    setPostText(existing?.caption || '');
    setSelectedImage(url);
    setIsCaptioning(true);
  };

  const handlePreview = (post: MediaI) => {
    setSelectedImage(post.uri);
    setIsDone(false);
    setPostText(post.caption ?? '');
    setIsCaptioning(true);
  };

  const handleDeletePost = () => {
    const updatedMedia = media.filter((item) => item.uri !== selectedImage);
    if (updatedMedia.length === 0) {
      setMedia([]);
      setSelectedImage(null);
      setPostText('');
      setIsCaptioning(false);
      return;
    }
    const newSelectedImage = updatedMedia[0].uri;
    setMedia(updatedMedia);
    setSelectedImage(newSelectedImage);
    setPostText(updatedMedia[0].caption || '');
  };

  const handlePost = async () => {
    if (!caption.trim() || isSubmitting) return;

    try {
      setIsSubmitting(true);

      if (type === 'SCRAPBOOK_POST') {
        if (editing && postId) {
          await dispatch(
            editScrapbookPost({
              payload: {
                text: caption.trim(),
                portUnLocode: portUnLocode!,
              },
              postId,
            }),
          ).unwrap();
        } else {
          await dispatch(
            createScrapbookPost({
              text: caption.trim(),
              portUnLocode: portUnLocode!,
            }),
          ).unwrap();
        }
      } else {
        let mediaData: PostMediaI[] = [];

        if (editing) {
          const newMedia = media?.filter((file) => file && !file.uri?.startsWith('http')) || [];
          const existingMedia = media?.filter((file) => file && file.uri?.startsWith('http')) || [];

          if (newMedia.length > 0) {
            const extensions = newMedia
              .map((file) => file?.type?.split('/')[1])
              .filter((ext) => ext);

            if (extensions.length > 0) {
              const response = await fetchPresignedUrlAPI(extensions, 'POST');

              if (!Array.isArray(response) || response.length !== newMedia.length) {
                throw new Error('Failed to get upload URLs');
              }

              await Promise.all(
                newMedia.map((file, index) => {
                  const presignedData = response[index];
                  return uploadFileWithPresignedUrl(file, presignedData.uploadUrl);
                }),
              );

              const newMediaData: PostMediaI[] = response.map((item, index) => ({
                fileUrl: item.accessUrl,
                caption: newMedia[index]?.caption || null,
                extension: newMedia[index]?.type?.split('/')[1] || 'jpeg',
              }));

              mediaData = [...mediaData, ...newMediaData];
            }
          }

          if (existingMedia.length > 0) {
            const existingMediaData: PostMediaI[] = existingMedia
              .filter((item) => item && originalMedia?.some((orig) => orig?.uri === item.uri))
              .map((item) => ({
                fileUrl: item.uri,
                caption: item.caption || null,
                extension: 'jpeg',
              }));

            mediaData = [...mediaData, ...existingMediaData];
          }
        } else {
          if (media?.length > 0) {
            const extensions = media.map((file) => file?.type?.split('/')[1]).filter((ext) => ext);

            if (extensions.length > 0) {
              const response = await fetchPresignedUrlAPI(extensions, 'POST');

              if (!Array.isArray(response) || response.length !== media.length) {
                throw new Error('Failed to get upload URLs');
              }

              await Promise.all(
                media.map((file, index) => {
                  const presignedData = response[index];
                  return uploadFileWithPresignedUrl(file, presignedData.uploadUrl);
                }),
              );

              mediaData = response.map((item, index) => ({
                fileUrl: item.accessUrl,
                caption: media[index]?.caption || null,
                extension: media[index]?.type?.split('/')[1] || 'jpeg',
              }));
            }
          }
        }

        const payload = {
          caption: caption.trim(),
          files: mediaData,
        };

        if (editing && postId) {
          await dispatch(
            editPost({
              payload,
              postId,
            }),
          ).unwrap();
        } else {
          await dispatch(createPost(payload)).unwrap();
        }
      }

      onSuccess();
      resetForm();
    } catch (error) {
      console.log(error);

      showToast({
        message: 'Error',
        description: editing
          ? 'Failed to update post'
          : type === 'SCRAPBOOK_POST'
            ? 'Failed to create scrapbook post'
            : 'Failed to create post',
        type: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    textInputRef.current?.clear();
    setCaption('');
    setMedia([]);
    setOriginalMedia([]);
    setSelectedImage(null);
    setPostText('');
    setIsCaptioning(false);
    setIsDone(false);
  };

  return {
    caption,
    setCaption,
    postText,
    setPostText,
    isSubmitting,
    isLoading,
    isCaptioning,
    setIsCaptioning,
    isDone,
    selectedImage,
    handlePostThumbnail,
    handleDeletePost,
    handlePreview,
    handleDone,
    handlePost,
    handleAttachments,
    textInputRef,
    media,
    setMedia,
  };
};

export default useCreatePostForm;
