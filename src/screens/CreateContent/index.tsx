/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { TouchableWithoutFeedback, KeyboardAvoidingView, Platform, Keyboard } from 'react-native';
import { RouteProp } from '@react-navigation/native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ScrollView } from 'react-native-gesture-handler';
import SafeArea from '@/src/components/SafeArea';
import { BottomTabNavigationI, CreateStackParamsListI } from '@/src/navigation/types';
import CreatePostForm from './components/CreatePostForm';

const CreatePostScreen = () => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const { params } = useRoute<RouteProp<CreateStackParamsListI, 'CreateContent'>>();

  const handleSuccess = () => {
    navigation.goBack();
  };

  return (
    <SafeArea>
      <KeyboardAvoidingView
        {...(Platform.OS === 'ios' ? { behavior: 'padding' } : {})}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="always">
          <CreatePostForm
            onSuccess={handleSuccess}
            type={params?.type ?? 'USER_POST'}
            {...(params?.portUnLocode && { portUnLocode: params.portUnLocode })}
            {...(params?.editing && { editing: params.editing })}
            {...(params?.postId && { postId: params.postId })}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeArea>
  );
};

export default CreatePostScreen;
