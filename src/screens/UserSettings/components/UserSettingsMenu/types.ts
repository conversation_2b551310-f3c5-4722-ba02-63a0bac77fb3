import type React from 'react';

export interface SettingsSectionProps {
  title: string;
  children: React.ReactNode;
}

export interface SettingsItemProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  rightElement?: React.ReactNode;
  onPress?: () => void;
  isLast?: boolean;
}

export interface ToggleSettingProps extends Omit<SettingsItemProps, 'rightElement'> {
  value: boolean;
  onValueChange: (value: boolean) => void;
}

export interface UserSettingsProps {
  onBack: () => void;
}

export interface SettingsState {
  notifications: {
    pushEnabled: boolean;
    emailEnabled: boolean;
    connectionRequests: boolean;
    messages: boolean;
    postInteractions: boolean;
    marketingEnabled: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'connections' | 'private';
    showActivity: boolean;
    showConnectionCount: boolean;
    allowEmailSearch: boolean;
    allowPhoneSearch: boolean;
  };
  dataStorage: {
    autoDownloadMedia: boolean;
    allowDataCollection: boolean;
  };
  appearance: {
    darkMode: boolean;
    language: string;
  };
  professional: {
    openToWork: boolean;
    showSalaryRange: boolean;
    careerInterests: boolean;
  };
}

export interface UseSettingsHookReturn {
  settings: SettingsState;
  isLoading: boolean;
  updateSetting: <T extends keyof SettingsState, K extends keyof SettingsState[T]>(
    category: T,
    setting: K,
    value: SettingsState[T][K],
  ) => Promise<void>;
  signOut: () => Promise<void>;
  deleteAccount: () => Promise<void>;
  isSignOutModalVisible: boolean;
  isDeleteAccountModalVisible: boolean;
  handleConfirmSignOut: () => Promise<void>;
  handleCancelSignOut: () => void;
  handleConfirmDeleteAccount: () => Promise<void>;
  handleCancelDeleteAccount: () => void;
}
