import type React from 'react';
import { View, Text, Switch, ScrollView, ActivityIndicator, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import CustomModal from '@/src/components/Modal';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import { version } from '@/package.json';
import type {
  SettingsSectionProps,
  SettingsItemProps,
  ToggleSettingProps,
  UserSettingsProps,
} from './types';
import { useSettings } from './useHook';

const SettingsSection: React.FC<SettingsSectionProps> = ({ title, children }) => {
  return (
    <View className="mb-6">
      <Text className="text-base font-semibold text-gray-800 mb-2">{title}</Text>
      <View className="bg-white rounded-xl overflow-hidden">{children}</View>
    </View>
  );
};

const SettingsItem: React.FC<SettingsItemProps> = ({
  title,
  description,
  icon,
  rightElement,
  onPress,
  isLast = false,
}) => {
  return (
    <Pressable
      onPress={onPress}
      disabled={!onPress}
      className={`flex-row items-center px-4 py-4 ${!isLast ? 'border-b border-gray-100' : ''}`}
    >
      {icon && <View className="mr-3">{icon}</View>}
      <View className="flex-1">
        <Text className="text-sm font-medium text-gray-800">{title}</Text>
        {description && <Text className="text-xs text-gray-500 mt-1">{description}</Text>}
      </View>
      {rightElement && <View>{rightElement}</View>}
    </Pressable>
  );
};

const ToggleSetting: React.FC<ToggleSettingProps> = ({
  title,
  description,
  icon,
  value,
  onValueChange,
  isLast,
}) => {
  return (
    <SettingsItem
      title={title}
      description={description}
      icon={icon}
      isLast={isLast}
      rightElement={
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#D1D5DB', true: '#4CAF50' }}
          thumbColor="#FFFFFF"
        />
      }
    />
  );
};

const UserSettingsMenu: React.FC<UserSettingsProps> = ({ onBack }) => {
  const currentUser = useSelector(selectCurrentUser);
  const navigation = useNavigation<BottomTabNavigationI>();

  const {
    settings,
    isLoading,
    updateSetting,
    signOut,
    deleteAccount,
    isSignOutModalVisible,
    isDeleteAccountModalVisible,
    handleConfirmSignOut,
    handleCancelSignOut,
    handleConfirmDeleteAccount,
    handleCancelDeleteAccount,
  } = useSettings(currentUser.profileId);

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50">
        <ActivityIndicator size="small" color="#4CAF50" />
        <Text className="mt-4 text-gray-600">Loading settings...</Text>
      </View>
    );
  }

  return (
    <>
      <CustomModal
        isVisible={isSignOutModalVisible}
        title="Sign Out"
        description="Are you sure you want to sign out?"
        confirmText="Sign Out"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={handleConfirmSignOut}
        onCancel={handleCancelSignOut}
      />

      <CustomModal
        isVisible={isDeleteAccountModalVisible}
        title="Delete Account"
        description="Are you sure you want to delete your account? This action cannot be undone."
        confirmText="Delete"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={handleConfirmDeleteAccount}
        onCancel={handleCancelDeleteAccount}
      />

      <BackButton onBack={onBack} label="" />
      <ScrollView className="flex-1 px-4 py-6 bg-gray-50">
        <SettingsSection title="Account">
          <SettingsItem
            title="Profile Information"
            description="Update your personal information"
            onPress={() => navigation.navigate('ProfileStack', { screen: 'EditUserProfile' })}
          />
        </SettingsSection>

        <SettingsSection title="Notifications">
          <ToggleSetting
            title="Push Notifications"
            description="Receive notifications on your device"
            value={settings.notifications.pushEnabled}
            onValueChange={(value) => updateSetting('notifications', 'pushEnabled', value)}
          />
          <ToggleSetting
            title="Connection Requests"
            description="Get notified when someone wants to connect"
            value={settings.notifications.connectionRequests}
            onValueChange={(value) => updateSetting('notifications', 'connectionRequests', value)}
          />
          <ToggleSetting
            title="Messages"
            description="Get notified about new messages"
            value={settings.notifications.messages}
            onValueChange={(value) => updateSetting('notifications', 'messages', value)}
          />
          <ToggleSetting
            title="Post Interactions"
            description="Get notified when someone likes or comments on your posts"
            value={settings.notifications.postInteractions}
            onValueChange={(value) => updateSetting('notifications', 'postInteractions', value)}
          />
          <ToggleSetting
            title="Marketing Communications"
            description="Receive updates about new features and promotions"
            value={settings.notifications.marketingEnabled}
            onValueChange={(value) => updateSetting('notifications', 'marketingEnabled', value)}
            isLast
          />
        </SettingsSection>

        <SettingsSection title="Privacy">
          <SettingsItem
            title="Blocked Profiles"
            description="Manage profiles you have blocked"
            onPress={() => navigation.navigate('ProfileStack', { screen: 'BlockedUserProfiles' })}
            isLast
          />
        </SettingsSection>

        <SettingsSection title="Support">
          <SettingsItem
            title="Terms of Service"
            description="Read our terms and conditions"
            onPress={() => navigation.navigate('ProfileStack', { screen: 'Terms' })}
          />
          <SettingsItem
            title="Privacy Policy"
            description="Read our privacy policy"
            onPress={() => navigation.navigate('ProfileStack', { screen: 'Privacy' })}
            isLast
          />
        </SettingsSection>

        <SettingsSection title="Account Actions">
          <SettingsItem title="Sign Out" description="Log out of your account" onPress={signOut} />
        </SettingsSection>

        <View className="py-6 items-center">
          <Text className="text-gray-500 text-sm">Version {version}</Text>
        </View>
      </ScrollView>
    </>
  );
};

export default UserSettingsMenu;
