import { useState, useEffect, useCallback } from 'react';
import notifee, { AndroidImportance } from '@notifee/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch } from 'react-redux';
import { signOutAsync } from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import type { SettingsState, UseSettingsHookReturn } from './types';

const SETTINGS_STORAGE_KEY = '@user_settings';

const defaultSettings: SettingsState = {
  notifications: {
    pushEnabled: true,
    emailEnabled: true,
    connectionRequests: true,
    messages: true,
    postInteractions: true,
    marketingEnabled: false,
  },
  privacy: {
    profileVisibility: 'connections',
    showActivity: true,
    showConnectionCount: true,
    allowEmailSearch: true,
    allowPhoneSearch: false,
  },
  dataStorage: {
    autoDownloadMedia: true,
    allowDataCollection: false,
  },
  appearance: {
    darkMode: false,
    language: 'English',
  },
  professional: {
    openToWork: false,
    showSalaryRange: false,
    careerInterests: true,
  },
};

export const useSettings = (userId: string): UseSettingsHookReturn => {
  const [settings, setSettings] = useState<SettingsState>(defaultSettings);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSignOutModalVisible, setIsSignOutModalVisible] = useState<boolean>(false);
  const [isDeleteAccountModalVisible, setIsDeleteAccountModalVisible] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const dispatch = useDispatch<AppDispatch>();

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const loadSettings = useCallback(async () => {
    try {
      const storedSettings = await AsyncStorage.getItem(`${SETTINGS_STORAGE_KEY}_${userId}`);
      if (storedSettings) {
        const parsedSettings = JSON.parse(storedSettings);
        setSettings({ ...defaultSettings, ...parsedSettings });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      setSettings(defaultSettings);
    }
  }, [userId]);

  const saveSettings = useCallback(
    async (newSettings: SettingsState) => {
      try {
        await AsyncStorage.setItem(
          `${SETTINGS_STORAGE_KEY}_${userId}`,
          JSON.stringify(newSettings),
        );
      } catch (error) {
        console.error('Failed to save settings:', error);
      }
    },
    [userId],
  );

  const setupNotificationChannels = useCallback(async () => {
    try {
      await notifee.createChannel({
        id: 'default',
        name: 'Default',
        importance: AndroidImportance.HIGH,
      });

      await notifee.createChannel({
        id: 'messages',
        name: 'Messages',
        description: 'New message notifications',
        importance: AndroidImportance.HIGH,
        sound: 'default',
      });

      await notifee.createChannel({
        id: 'connections',
        name: 'Connections',
        description: 'Connection request notifications',
        importance: AndroidImportance.DEFAULT,
      });

      await notifee.createChannel({
        id: 'posts',
        name: 'Post Interactions',
        description: 'Likes, comments, and shares on your posts',
        importance: AndroidImportance.DEFAULT,
      });

      await notifee.createChannel({
        id: 'marketing',
        name: 'Marketing',
        description: 'Updates and promotional content',
        importance: AndroidImportance.LOW,
      });
    } catch (error) {
      console.error('Failed to setup notification channels:', error);
    }
  }, []);

  const requestNotificationPermission = useCallback(async () => {
    try {
      const settings = await notifee.requestPermission();
      return settings.authorizationStatus === 1;
    } catch (error) {
      console.error('Failed to request notification permission:', error);
      return false;
    }
  }, []);

  useEffect(() => {
    const initializeSettings = async () => {
      try {
        setIsLoading(true);
        setError(null);
        await loadSettings();
        await setupNotificationChannels();
      } catch (error) {
        const errorMessage = `Failed to initialize settings: ${error instanceof Error ? error.message : 'Unknown error'}`;
        triggerErrorBoundary(new Error(errorMessage));
      } finally {
        setIsLoading(false);
      }
    };

    initializeSettings();
  }, [userId, loadSettings, setupNotificationChannels, triggerErrorBoundary]);

  const updateSetting = async <T extends keyof SettingsState, K extends keyof SettingsState[T]>(
    category: T,
    setting: K,
    value: SettingsState[T][K],
  ): Promise<void> => {
    const previousSettings = { ...settings };

    try {
      const newSettings = {
        ...settings,
        [category]: {
          ...settings[category],
          [setting]: value,
        },
      };

      setSettings(newSettings);

      if (category === 'notifications' && setting === 'pushEnabled' && value === true) {
        const hasPermission = await requestNotificationPermission();
        if (!hasPermission) {
          setSettings(previousSettings);
          showToast({
            message: 'Permission Required',
            description: 'Please enable notifications in your device settings',
            type: 'info',
          });
          return;
        }
      }

      await saveSettings(newSettings);

      showToast({
        message: 'Settings Updated',
        description: 'Your preferences have been saved',
        type: 'success',
      });
    } catch (error) {
      setSettings(previousSettings);
      const errorMessage = `Failed to update setting: ${error instanceof Error ? error.message : 'Unknown error'}`;
      triggerErrorBoundary(new Error(errorMessage));
    }
  };

  const signOut = async (): Promise<void> => {
    return new Promise((resolve) => {
      setIsSignOutModalVisible(true);
      resolve();
    });
  };

  const handleConfirmSignOut = async (): Promise<void> => {
    try {
      setIsSignOutModalVisible(false);
      await new Promise((resolve) => setTimeout(resolve, 500));
      await dispatch(signOutAsync()).unwrap();
    } catch (error) {
      const errorMessage = `Failed to sign out: ${error instanceof Error ? error.message : 'Unknown error'}`;

      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Sign Out Failed',
            description: 'Unable to sign out. Please try again.',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later.',
            type: 'error',
          });
        },
      });

      triggerErrorBoundary(new Error(errorMessage));
    }
  };

  const handleCancelSignOut = (): void => {
    setIsSignOutModalVisible(false);
  };

  const deleteAccount = async (): Promise<void> => {
    return new Promise((resolve) => {
      setIsDeleteAccountModalVisible(true);
      resolve();
    });
  };

  const handleConfirmDeleteAccount = async (): Promise<void> => {
    try {
      setIsDeleteAccountModalVisible(false);
      await new Promise((resolve) => setTimeout(resolve, 500));

      await AsyncStorage.removeItem(`${SETTINGS_STORAGE_KEY}_${userId}`);

      showToast({
        message: 'Account Deleted',
        description: 'Your account has been permanently deleted',
        type: 'success',
      });
    } catch (error) {
      const errorMessage = `Failed to delete account: ${error instanceof Error ? error.message : 'Unknown error'}`;
      triggerErrorBoundary(new Error(errorMessage));
    }
  };

  const handleCancelDeleteAccount = (): void => {
    setIsDeleteAccountModalVisible(false);
  };

  return {
    settings,
    isLoading,
    updateSetting,
    signOut,
    deleteAccount,
    isSignOutModalVisible,
    isDeleteAccountModalVisible,
    handleConfirmSignOut,
    handleCancelSignOut,
    handleConfirmDeleteAccount,
    handleCancelDeleteAccount,
  };
};
