/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import Button from '@/src/components/Button';
import TextInput from '@/src/components/TextInput';
import TextView from '@/src/components/TextView';
import { AuthStackParamListI } from '@/src/navigation/types';
import useSetUsername from './useHook';

const SetUsernameForm = () => {
  const route = useRoute<RouteProp<AuthStackParamListI, 'SetUsername'>>();
  const email = route.params?.email || '';
  const { methods, isSubmitting, onSubmit, isUsernameAvailable } = useSetUsername(email);

  const {
    control,
    handleSubmit,
    formState: { isValid, errors },
  } = methods;

  return (
    <View className="flex-1 bg-white">
      <View className="px-5">
        <View className="my-8">
          <TextView title="Set username" subtitle="Choose a unique username for your account" />
        </View>
        <View>
          <Controller
            control={control}
            name="userName"
            rules={{
              required: 'Username is required',
              pattern: {
                value: /^(?![_.])[a-zA-Z0-9._]{2,25}(?<![_.])$/,
                message: 'Invalid Username',
              },
              validate: () => isUsernameAvailable || 'Username is already taken',
            }}
            render={({ field: { onChange, value } }) => (
              <TextInput
                label="Username"
                placeholder="Enter username"
                value={value}
                onChangeText={onChange}
                error={errors.userName?.message}
                editable
              />
            )}
          />
        </View>
        <View className="mt-6">
          <Controller
            control={control}
            name="email"
            render={({ field: { value } }) => (
              <TextInput label="Email ID" value={value} editable={false} />
            )}
          />
        </View>
        <View className="mt-8">
          <Button
            onPress={handleSubmit(onSubmit)}
            disabled={!isValid || isSubmitting || !isUsernameAvailable}
            label="Save username"
            variant={isValid && isUsernameAvailable ? 'primary' : 'tertiary'}
            loading={isSubmitting}
            labelClassName="text-base font-medium"
          />
        </View>
      </View>
    </View>
  );
};

export default SetUsernameForm;
