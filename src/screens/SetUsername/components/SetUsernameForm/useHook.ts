/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { saveUsernameAsync } from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { AppStackParamListI } from '@/src/navigation/types';
import { checkUsernameAPI } from '@/src/networks/profile/username';
import { SetUsernameFormDataI, UseSetUsernameFormI } from './types';

const useSetUsername = (email: string): UseSetUsernameFormI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUsernameAvailable, setIsUsernameAvailable] = useState(true);

  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();
  const dispatch = useDispatch<AppDispatch>();

  const defaultValues = {
    userName: '',
    email,
  };

  const methods = useForm<SetUsernameFormDataI>({
    mode: 'onChange',
    defaultValues,
  });

  const { watch, setError, clearErrors } = methods;
  const userName = watch('userName').trim();

  useEffect(() => {
    if (email) {
      methods.setValue('email', email);
    }
  }, [email]);

  const checkUsername = async (username: string) => {
    if (!username) {
      setIsUsernameAvailable(true);
      clearErrors('userName');
      return;
    }

    try {
      await checkUsernameAPI({ username });
      setIsUsernameAvailable(true);
      clearErrors('userName');
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          if (error instanceof APIResError && error.status === 409) {
            setIsUsernameAvailable(false);
            setError('userName', {
              message: 'Username is already taken',
            });
            return;
          }
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    }
  };

  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      if (userName) {
        checkUsername(userName);
      }
    }, 500);

    return () => clearTimeout(debounceTimeout);
  }, [userName]);

  const onSubmit = async (data: SetUsernameFormDataI) => {
    setIsSubmitting(true);
    try {
      await dispatch(
        saveUsernameAsync({
          username: data.userName.trim(),
        }),
      ).unwrap();
      navigation.navigate('AddUserDetailScreen');
    } catch (error) {
      setIsSubmitting(false);
      handleError(error);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    isUsernameAvailable,
  };
};

export default useSetUsername;
