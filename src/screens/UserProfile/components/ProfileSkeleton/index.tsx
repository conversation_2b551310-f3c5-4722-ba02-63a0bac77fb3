/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View } from 'react-native';
import ShimmerBox from '@/src/components/Shimmer';

const ProfileSkeleton = () => {
  return (
    <View className="bg-white flex-1">
      <View className="flex-row items-center justify-between px-4 py-3">
        <ShimmerBox width={24} height={24} />
        <ShimmerBox width={120} height={20} borderRadius={20} />
        <ShimmerBox width={24} height={24} />
      </View>
      <View className="px-4">
        <View className="flex-row mb-4">
          <View className="mr-4">
            <ShimmerBox width={80} height={80} variant="sphere" />
          </View>
          <View className="justify-center flex-1">
            <View className="flex-row items-center mb-2">
              <ShimmerBox width={100} height={24} borderRadius={4} />
              <ShimmerBox width={20} height={20} variant="sphere" className="ml-2" />
            </View>
            <ShimmerBox width={'90%'} height={18} borderRadius={4} />
          </View>
        </View>
      </View>
      <View className="flex-row border-b border-gray-200">
        <View className="flex-1 items-center py-3">
          <ShimmerBox width={60} height={18} borderRadius={4} />
        </View>
        <View className="flex-1 items-center py-3">
          <ShimmerBox width={60} height={18} borderRadius={4} />
        </View>
        <View className="flex-1 items-center py-3 border-b-2 border-green-700">
          <ShimmerBox width={60} height={18} borderRadius={4} />
        </View>
      </View>
      <View className="px-4 mt-6 flex-1">
        <ShimmerBox width={'100%'} height={180} borderRadius={8} className="mb-4" />
        <ShimmerBox width={'100%'} height={180} borderRadius={8} className="mb-4" />
        <ShimmerBox width={'100%'} height={180} borderRadius={8} className="mb-4" />
      </View>
    </View>
  );
};

export default ProfileSkeleton;
