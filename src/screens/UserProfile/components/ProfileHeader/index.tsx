import { useState } from 'react';
import React from 'react';
import { View, Text, Pressable, ActivityIndicator, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import CustomModal from '@/src/components/Modal';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { capitalizeFirstLetter } from '@/src/utilities/data/string';
import { showToast } from '@/src/utilities/toast';
import { AppStackParamListI, BottomTabNavigationI, ConnectionTypeI } from '@/src/navigation/types';
import AddConnection from '@/src/assets/svgs/AddConnection';
import Close from '@/src/assets/svgs/Close';
import MessageIcon from '@/src/assets/svgs/Message';
import DotSmall from '@/src/assets/svgs/SmallDot';
import Tick from '@/src/assets/svgs/Tick';
import VerifiedBadgeIcon from '@/src/assets/svgs/VerifiedBadge';
import { deleteConnectionAPI, sendConnectionRequestAPI } from '@/src/networks/connect/connection';
import { respondReceivedRequestAPI } from '@/src/networks/connect/request';
import { RequestStatusTypeE } from '@/src/networks/connect/types';
import { ProfileHeaderProps } from './types';

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  data,
  isUserProfile,
  aboutProfile,
  setFollowing,
}) => {
  const [connecting, setConnecting] = useState(false);
  const navigation = useNavigation<BottomTabNavigationI>();
  const state = navigation.getState();
  const activeRouteName = state.routes[state.index].name as keyof AppStackParamListI;
  const currentUser = useSelector(selectCurrentUser);
  const [isPendingRequest, setIsPendingRequest] = useState(
    aboutProfile?.request?.status === 'PENDING',
  );
  const [isSelfRequested, setIsSelfRequested] = useState(
    aboutProfile?.request?.senderRequestId === currentUser.profileId,
  );
  const [isConnected, setIsConnected] = useState(aboutProfile?.request?.status === 'CONNECTED');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalAction, setModalAction] = useState<'disconnect' | 'revoke' | 'reject' | null>(null);

  const isRequestSent = isPendingRequest && isSelfRequested;
  const isRequestReceived =
    isPendingRequest && aboutProfile?.request?.senderRequestId !== currentUser.profileId;
  const hasMutuals = aboutProfile?.mutuals?.length;
  const showDot = isUserProfile || hasMutuals;

  const handleNavigateToConnections = (type: ConnectionTypeI) => {
    if (activeRouteName === 'UserProfile') {
      navigation.navigate('ProfileStack', {
        screen: 'Connection',
        params: {
          profileId: data.profileId,
          type,
          isUserProfile,
        },
      });
    } else {
      navigation.navigate('HomeStack', {
        screen: 'Connection',
        params: {
          profileId: data.profileId,
          type,
          isUserProfile,
        },
      });
    }
  };

  const onConnectionReqPress = async () => {
    if (!data?.profileId) return;

    try {
      setConnecting(true);
      const connectionStates = {
        connected: {
          nextStatus: 'DISCONNECTED',
          action: 'disconnected',
          apiCall: () => deleteConnectionAPI({ profileId: data.profileId }),
          stateUpdate: () => {
            setIsConnected(false);
            setFollowing(false);
          },
        },
        pending: {
          nextStatus: 'REVOKED',
          action: 'revoked',
          apiCall: () =>
            sendConnectionRequestAPI({
              receiverProfileId: data.profileId,
              requestedStatus: 'REVOKED',
            }),
          stateUpdate: () => {
            setIsPendingRequest(false);
            setIsSelfRequested(false);
          },
        },
        default: {
          nextStatus: 'PENDING',
          action: 'sent',
          apiCall: () =>
            sendConnectionRequestAPI({
              receiverProfileId: data.profileId,
              requestedStatus: 'PENDING',
            }),
          stateUpdate: () => {
            setIsPendingRequest(true);
            setIsSelfRequested(true);
            setFollowing(true);
          },
        },
      };

      const currentState = isConnected ? 'connected' : isPendingRequest ? 'pending' : 'default';

      const stateHandler = connectionStates[currentState];

      await stateHandler.apiCall();

      stateHandler.stateUpdate();

      showToast({
        type: 'success',
        message: 'Success',
        description: `Connection request ${stateHandler.action} successfully.`,
      });
    } catch (err) {
      const actionVerb = isConnected
        ? 'disconnect from'
        : isPendingRequest
          ? 'revoke request to'
          : 'send request to';

      showToast({
        type: 'error',
        message: 'Error',
        description: `Failed to ${actionVerb} this profile`,
      });
    } finally {
      setConnecting(false);
    }
  };

  const handleConnectionRequest = async (status: RequestStatusTypeE) => {
    if (!data?.profileId) return;

    try {
      setConnecting(true);

      if (!aboutProfile?.request?.senderRequestId) {
        throw new Error('Sender Request Id not found');
      }

      await respondReceivedRequestAPI({
        requestedStatus: status,
        senderProfileId: aboutProfile.request.senderRequestId,
      });

      if (status === 'ACCEPTED') {
        setIsConnected(true);
        setIsPendingRequest(false);
        setFollowing(true);
      } else {
        setIsPendingRequest(false);
        setFollowing(false);
      }

      showToast({
        type: 'success',
        message: 'Success',
        description: `Connection request ${status.toLowerCase()}ed successfully.`,
      });
    } catch (err) {
      showToast({
        type: 'error',
        message: 'Error',
        description: `Failed to ${status.toLowerCase()} request`,
      });
    } finally {
      setConnecting(false);
    }
  };

  const onAcceptRequest = () => handleConnectionRequest('ACCEPTED');
  const onRejectRequest = () => handleConnectionRequest('REJECTED');

  const handleShowConfirmation = (action: 'disconnect' | 'reject' | 'revoke') => {
    setModalAction(action);
    setIsModalVisible(true);
  };

  const handleConfirmAction = async () => {
    if (!data?.profileId || !modalAction) return;

    try {
      if (isConnected || (isPendingRequest && isSelfRequested)) {
        await onConnectionReqPress();
      } else {
        await onRejectRequest();
      }
    } catch (err) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'An error occurred while processing your request.',
      });
    } finally {
      setConnecting(false);
      setIsModalVisible(false);
      setModalAction(null);
    }
  };

  const handleModalClose = () => {
    setIsModalVisible(false);
    setTimeout(() => {
      setModalAction(null);
    }, 300);
  };

  if (!data) return null;

  const displayDesignation = () => {
    if (!data.designation?.name) return null;
    if (data.designation.name === 'Aspirant') {
      return (
        <Text className="font-inter font-normal text-[#171717] mt-2">{data.designation.name}</Text>
      );
    }
    return (
      <Text className="font-inter font-normal text-[#171717] mt-2">
        {`${data.designation.name}${data.entity?.name ? ` at ${data.entity.name}` : ''}`}
      </Text>
    );
  };

  const renderConnectionButtons = () => {
    if (isUserProfile) return null;

    if (isConnected) {
      return (
        <Pressable
          onPress={() => handleShowConfirmation('disconnect')}
          disabled={connecting}
          className="border border-1px flex-row items-center justify-center border-[#448600] bg-white rounded-[8px] w-3/4"
        >
          <View className="px-1">
            {connecting ? (
              <ActivityIndicator color="#448600" />
            ) : (
              <Tick color="#448600" width={1.5} height={1.5} />
            )}
          </View>
          <Text className="text-[#448600]">Connected</Text>
        </Pressable>
      );
    } else if (isRequestSent) {
      return (
        <Pressable
          onPress={() => handleShowConfirmation('revoke')}
          disabled={connecting}
          className="border border-1px flex-row items-center justify-center border-[#448600] bg-[#448600] rounded-[8px] w-3/4"
        >
          <View className="px-1">
            {connecting ? (
              <ActivityIndicator color="white" />
            ) : (
              <Tick color="white" width={1.5} height={1.5} />
            )}
          </View>
          <Text className="text-white">Requested</Text>
        </Pressable>
      );
    } else if (isRequestReceived) {
      return (
        <View className="flex-row gap-2">
          <Pressable
            disabled={connecting}
            onPress={onAcceptRequest}
            className="border border-1px flex-row items-center justify-center border-[#448600] bg-[#448600] rounded-[8px] px-1"
          >
            <View className="px-1">
              {connecting ? (
                <ActivityIndicator color="white" />
              ) : (
                <Tick color="white" width={1.5} height={1.5} />
              )}
            </View>
            <Text className="text-white">Accept</Text>
          </Pressable>
          <Pressable
            disabled={connecting}
            onPress={() => handleShowConfirmation('reject')}
            className="border border-1px flex-row items-center justify-center border-[#D4D4D4] bg-white rounded-[8px] px-1"
          >
            <View className="px-1">
              {connecting ? (
                <ActivityIndicator color="white" />
              ) : (
                <Close width={1.75} height={1.75} color="black" />
              )}
            </View>
            <Text className="text-[#171717]">Reject</Text>
          </Pressable>
        </View>
      );
    } else {
      return (
        <Pressable
          disabled={connecting}
          onPress={onConnectionReqPress}
          className="border border-1px flex-row items-center justify-center border-[#448600] bg-[#448600] rounded-[8px] w-3/4"
        >
          <View className="px-1">
            {connecting ? <ActivityIndicator color="white" /> : <AddConnection color="white" />}
          </View>
          <Text className="text-white">Connect</Text>
        </Pressable>
      );
    }
  };

  return (
    <>
      <View className="flex-row gap-4 px-4">
        <View>
          <UserAvatar
            avatarUri={data.avatar}
            name={data.name || 'Anonymous'}
            width={80}
            height={80}
            className="my-2"
          />
        </View>
        <View className="flex-1 mt-2 gap-1">
          <View className="flex-row items-center">
            <Text className="text-xl font-medium" numberOfLines={1}>
              {data.name}
            </Text>
            <View className="pl-1">
              <VerifiedBadgeIcon />
            </View>
          </View>
          {displayDesignation()}
          <View className="flex-row items-center mt-2">
            <Pressable onPress={() => handleNavigateToConnections('followers')}>
              <Text className="font-inter font-medium text-[#448600]">
                {`${aboutProfile?.followersCount ?? 0} followers`}
              </Text>
            </Pressable>

            {showDot ? (
              <View className="pt-1 mx-1">
                <DotSmall />
              </View>
            ) : null}

            {isUserProfile ? (
              <Pressable onPress={() => handleNavigateToConnections('following')}>
                <Text className="font-inter font-medium text-[#448600]">
                  {`${aboutProfile?.followingsCount ?? 0} following`}
                </Text>
              </Pressable>
            ) : null}

            {!isUserProfile && hasMutuals ? (
              <>
                <Pressable
                  onPress={() => handleNavigateToConnections('mutuals')}
                  className="flex-row items-center"
                >
                  <View className="flex-row items-center">
                    <View className="flex-row">
                      {aboutProfile.mutuals.map((item, index) => (
                        <Image
                          key={index}
                          source={{ uri: item.avatar }}
                          className={`w-[20px] h-[20px] rounded-full ${index !== 0 ? '-ml-3' : ''}`}
                        />
                      ))}
                    </View>
                    <Text className="font-inter text-[#737373] font-medium ml-1">
                      {`${aboutProfile.mutuals.length} mutual${aboutProfile.mutuals.length !== 1 ? 's' : ''}`}
                    </Text>
                  </View>
                </Pressable>
              </>
            ) : null}
          </View>
          {!isUserProfile ? (
            <View className="flex-row h-[36px] gap-3 mt-2">
              {renderConnectionButtons()}
              <Pressable
                onPress={() => navigation.navigate('Chat', { id: data.profileId })}
                className="border border-1px rounded-[8px] border-[#D4D4D4] justify-center px-2"
              >
                <MessageIcon />
              </Pressable>
            </View>
          ) : (
            <View className="flex-row h-[36px] gap-3 mt-2">
              <Pressable
                onPress={() => handleNavigateToConnections('connections')}
                className="border border-1px flex-row gap-2 items-center justify-center border-[#448600] bg-white rounded-[8px] w-3/4"
              >
                <AddConnection color="#448600" />
                <Text className="text-[#448600]">My Networks</Text>
              </Pressable>
              <Pressable onPress={() => handleNavigateToConnections('requests_received')}>
                <View className="border border-1px rounded-[8px] border-[#D4D4D4] justify-center items-center p-2">
                  <AddConnection />
                </View>
              </Pressable>
            </View>
          )}
        </View>
      </View>
      <View>
        <CustomModal
          isVisible={isModalVisible}
          title="Confirm Action"
          description={
            modalAction === 'disconnect'
              ? 'Are you sure you want to disconnect?'
              : modalAction === 'revoke'
                ? 'Are you sure you want to revoke the connection request?'
                : modalAction === 'reject'
                  ? 'Are you sure you want to reject the connection request?'
                  : ''
          }
          cancelText="Cancel"
          confirmText={capitalizeFirstLetter(modalAction ?? '')}
          onConfirm={handleConfirmAction}
          confirmButtonVariant="danger"
          isConfirming={connecting}
          onCancel={handleModalClose}
        />
      </View>
    </>
  );
};

export default ProfileHeader;
