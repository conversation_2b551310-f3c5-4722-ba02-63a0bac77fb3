import { ScrollView, Text } from 'react-native';
import { useSelector } from 'react-redux';
import { selectProfileDescription } from '@/src/redux/selectors/about';
import ProfileCertification from './components/ProfileCertification';
import ProfileDocumentation from './components/ProfileDocumentation';
import ProfileEducation from './components/ProfileEducation';
import ProfileSkill from './components/ProfileSkills';
import { AboutScreenPropsI } from './types';

const AboutScreen = ({ isUserProfile }: AboutScreenPropsI) => {
  const description = useSelector(selectProfileDescription);
  return (
    <ScrollView showsVerticalScrollIndicator={false} className="px-4">
      {description && <Text className="pt-8 pb-4">{description}</Text>}
      <ProfileEducation isUserProfile={isUserProfile} />
      <ProfileCertification isUserProfile={isUserProfile} />
      <ProfileDocumentation isUserProfile={isUserProfile} />
      <ProfileSkill isUserProfile={isUserProfile} />
    </ScrollView>
  );
};

export default AboutScreen;
