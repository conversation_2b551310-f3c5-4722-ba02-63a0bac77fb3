import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdTypeI } from '@/src/types/common/data';
import { AboutProfileI } from '../ProfileContent/types';

export interface CertificateI {
  id: string;
  name: string;
  entity: SearchResultI;
  fromDate: string; // ISO format, can be converted to Date if needed
  untilDate: string;
}

export interface MetaI {
  educationCount: number;
  statutoryCertCount: number;
  valueAddedCertCount: number;
  identityCount: number;
  visaCount: number;
  maritimeSkillsTotal: number;
  mutualTotal: number;
}

export interface AboutScreenPropsI {
  isUserProfile: boolean;
}
