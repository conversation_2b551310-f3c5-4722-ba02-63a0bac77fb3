import { useSelector } from 'react-redux';
import {
  selectAboutProfileCertificationsStatutoryCount,
  selectAboutProfileStatutoryCertifications,
} from '@/src/redux/selectors/about';
import { navigate } from '@/src/utilities/navigation';

export const UseProfileStatutory = () => {
  const certifications = useSelector(selectAboutProfileStatutoryCertifications);
  const count = useSelector(selectAboutProfileCertificationsStatutoryCount);

  const onListCertifications = () => {
    navigate('EditCertificationList', {
      editable: false,
      tab: 'statutory',
    });
  };

  return {
    certifications,
    count,
    onListCertifications,
  };
};
