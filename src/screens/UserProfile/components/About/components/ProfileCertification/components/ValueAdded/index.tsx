import { ActivityIndicator, Text, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';
import Accordion from '@/src/components/Accordion';
import NotFound from '@/src/components/NotFound';
import { RootState } from '@/src/redux/store';
import { navigate } from '@/src/utilities/navigation';
import { CertificationExpand } from '../ExpandCertification';
import { ValueAddedPropsI } from './types';
import { useProfileValueAdded } from './useHook';

const ProfileValueAdded = () => {
  const { certifications, count, loading } = useProfileValueAdded();

  if (loading) {
    return <ActivityIndicator className="pt-14 pb-6" />;
  }

  return (
    <View>
      {certifications.slice(0, 3).map((certification, index) => (
        <Accordion
          key={certification.id}
          title={certification.certificateCourse.name}
          subTitle={certification.entity.name}
          content={<CertificationExpand certification={certification} />}
          isLast={count > 3 ? index === 2 : index === count - 1}
        />
      ))}
      {count > 3 && (
        <TouchableOpacity
          onPress={() => navigate('EditCertificationList', { editable: false, tab: 'valueAdded' })}
        >
          <Text className="text-[#448600] text-base font-medium pt-4">
            {`View all ${count} value added certifications`}
          </Text>
        </TouchableOpacity>
      )}
      {count === 0 && (
        <NotFound
          // title="No Value Added Certifications Found"
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default ProfileValueAdded;
