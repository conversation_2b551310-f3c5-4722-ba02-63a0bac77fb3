/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Text, TouchableOpacity, View } from 'react-native';
import Accordion from '@/src/components/Accordion';
import NotFound from '@/src/components/NotFound';
import { CertificationExpand } from '../ExpandCertification';
import { UseProfileStatutory } from './useHook';

const ProfileStatutory = () => {
  const { certifications, count, onListCertifications } = UseProfileStatutory();

  return (
    <View>
      {(certifications.length > 3 ? certifications.slice(0, 3) : certifications).map(
        (certification, index, arr) => (
          <Accordion
            key={certification.id}
            title={certification.name!}
            subTitle={certification.entity.name}
            content={<CertificationExpand certification={certification} />}
            isLast={index === arr.length - 1}
          />
        ),
      )}
      {count > 3 && (
        <TouchableOpacity onPress={onListCertifications}>
          <Text className="text-[#448600] text-base font-medium pt-4">
            {`View all ${count} statutory certifications`}
          </Text>
        </TouchableOpacity>
      )}
      {count === 0 && (
        <NotFound
          // title="No Statutory Certifications Found"
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default ProfileStatutory;
