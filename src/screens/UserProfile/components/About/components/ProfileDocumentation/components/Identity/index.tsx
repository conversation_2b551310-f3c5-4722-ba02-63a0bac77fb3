import { Text, TouchableOpacity, View } from 'react-native';
import NotFound from '@/src/components/NotFound';
import { navigate } from '@/src/utilities/navigation';
import { IdentityDocumentDetails } from '../DocumentDetails';
import { expiresIn } from '../utils';
import { IdentityDocumentI, ProfileIdentityPropsI } from './types';
import { useProfileIdentity } from './useHook';

const ProfileIdentity = () => {
  const { documents, count } = useProfileIdentity();

  return (
    <View>
      {documents.map((item: IdentityDocumentI, index: number) => {
        return (
          <IdentityDocumentDetails
            key={index}
            document={item}
            isLast={index === documents.length - 1}
            expiresIn={expiresIn(item.untilDate)}
          />
        );
      })}
      {count > 3 && (
        <TouchableOpacity
          onPress={() =>
            navigate('EditDocumentList', {
              editable: false,
              tab: 'identity',
            })
          }
        >
          <Text className="text-[#448600] text-base font-medium pt-4">
            {`View all ${count} identity documents`}
          </Text>
        </TouchableOpacity>
      )}
      {count === 0 && (
        <NotFound
          // title="No Identity Documents Found"
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default ProfileIdentity;
