import { ActivityIndicator, Text, TouchableOpacity, View } from 'react-native';
import NotFound from '@/src/components/NotFound';
import { navigate } from '@/src/utilities/navigation';
import { VisaDocumentDetails } from '../DocumentDetails';
import { expiresIn } from '../utils';
import { ProfileVisaDocumentationI } from './types';
import { useProfileVisa } from './useHook';

const ProfileVisa = () => {
  const { documentations, count, loading } = useProfileVisa();

  if (loading) {
    return <ActivityIndicator className="pt-14 pb-6" />;
  }

  return (
    <View>
      {documentations.map((item: ProfileVisaDocumentationI, index: number) => (
        <VisaDocumentDetails
          key={index}
          document={item}
          isLast={index === documentations.length - 1}
          expiresIn={expiresIn(item.untilDate)}
        />
      ))}
      {count > 3 && (
        <TouchableOpacity
          onPress={() =>
            navigate('EditDocumentList', {
              editable: false,
              tab: 'visa',
            })
          }
        >
          <Text className="text-[#448600] text-base font-medium pt-4">
            {`View all ${count} visa documents`}
          </Text>
        </TouchableOpacity>
      )}
      {count === 0 && (
        <NotFound
          // title="No Visa Documents Found"
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default ProfileVisa;
