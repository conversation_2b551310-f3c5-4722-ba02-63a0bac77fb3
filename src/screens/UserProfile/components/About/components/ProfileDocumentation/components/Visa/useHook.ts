import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectAboutProfileDocumentsVisaCount } from '@/src/redux/selectors/about';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { fetchVisaDocumentationsAPI } from '@/src/networks/profile/visaDocumentations';
import { ProfileVisaDocumentationI } from './types';

export const useProfileVisa = () => {
  const [documentations, seDocumentations] = useState<ProfileVisaDocumentationI[]>([]);
  const count = useSelector(selectAboutProfileDocumentsVisaCount);
  const { profileId } = useSelector(selectCurrentUser);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchDocumentations = async () => {
      try {
        setLoading(true);
        const response = await fetchVisaDocumentationsAPI(profileId);
        seDocumentations(response);
      } catch (error) {
        handleError(error, {
          handle4xxError: () => {
            showToast({
              message: 'Failed to Fetch Certifications',
              type: 'error',
            });
          },
          handle5xxError: () => {
            showToast({
              message: 'Server Error',
              description: 'Please try again later',
              type: 'error',
            });
          },
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDocumentations();
  }, [profileId]);

  return {
    documentations,
    count,
    loading,
  };
};
