import { useState } from 'react';
import { useSelector } from 'react-redux';
import {
  selectAboutProfileMaritimeSkills,
  selectAboutProfileMaritimeSkillsCount,
} from '@/src/redux/selectors/about';

export const useMaritimeSkills = () => {
  const maritimeSkills = useSelector(selectAboutProfileMaritimeSkills);
  const count = useSelector(selectAboutProfileMaritimeSkillsCount);
  const [localSkills, setLocalSkills] = useState(maritimeSkills);

  return {
    localSkills,
    count,
  };
};
