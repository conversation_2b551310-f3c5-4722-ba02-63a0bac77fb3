import { ActivityIndicator, View } from 'react-native';
import ChipInput from '@/src/components/ChipInput';
import NotFound from '@/src/components/NotFound';
import { useOtherSkills } from './useHook';

const OtherSkills = () => {
  const { localSkills, loading } = useOtherSkills();

  const onPress = () => {
    return;
  };

  if (loading) {
    return <ActivityIndicator className="pt-14 pb-6" />;
  }

  return (
    <View className="p-2">
      <View className="mb-6">
        <ChipInput
          title="Skills"
          placeholder="Add a skill"
          chips={localSkills.slice(0, 3)}
          disabled={true}
          removable={false}
          showBorder={false}
          showTitle={false}
        />
      </View>
      {localSkills.length === 0 && (
        <NotFound
          // title="No Other Skills Found"
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default OtherSkills;
