import { Text, View } from 'react-native';
import ChipInput from '@/src/components/ChipInput';
import NotFound from '@/src/components/NotFound';
import { MaritimeSkillsPropsI } from './types';
import { useMaritimeSkills } from './useHook';

const MaritimeSkills = () => {
  const { localSkills, count } = useMaritimeSkills();

  const onPress = () => {
    return;
  };

  return (
    <View className="p-2">
      <View className="mb-6">
        <ChipInput
          title="Skills"
          placeholder="Add a skill"
          chips={localSkills}
          disabled={true}
          removable={false}
          showBorder={false}
          showTitle={false}
        />
      </View>
      {count === 0 && (
        <NotFound
          // title="No Maritime Skills  Found"
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default MaritimeSkills;
