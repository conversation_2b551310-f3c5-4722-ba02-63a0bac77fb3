import { useEffect } from 'react';
import { ActivityIndicator, TouchableOpacity, View } from 'react-native';
import NotFound from '@/src/components/NotFound';
import { SectionHeader } from '@/src/components/SectionHeader';
import EditPencil from '@/src/assets/svgs/EditPencil';
import Flag from '@/src/assets/svgs/Flag';
import Suitcase from '@/src/assets/svgs/Suitcase';
import CompanyExperience from '../CompanyExperience';
import PortsVisited from '../Ports';
import { ProfileExperiencePropsI } from './types';
import useProfileExperience from './useHook';

const ProfileExperience = ({ isUserProfile, profileId }: ProfileExperiencePropsI) => {
  const { experiences, fetchExperiences, handleEdit, loading } = useProfileExperience(profileId);

  if (loading) {
    return <ActivityIndicator />;
  }
  return (
    <View className="flex gap-6">
      <View className="bg-white">
        <View className="flex-row items-center justify-between">
          <SectionHeader title="Experience" icon={Suitcase} />
          {isUserProfile && (
            <TouchableOpacity onPress={handleEdit}>
              <EditPencil width={2.5} height={2.5} />
            </TouchableOpacity>
          )}
        </View>
        {experiences.data.length ? (
          experiences.data.map((experience, index) => {
            return (
              <CompanyExperience
                data={experience}
                isFirst={index === 0}
                isLast={index === experiences.data.length - 1}
                key={index}
              />
            );
          })
        ) : (
          <NotFound fullScreen={false} className="py-10" imageStyle={{ height: 120, width: 120 }} />
        )}
      </View>

      <View>
        <SectionHeader title="Ports visited" icon={Flag} containerClassName="mb-5" />
        <PortsVisited
          ports={experiences.portVisits.data}
          total={experiences.portVisits.total}
          profileId={profileId}
        />
      </View>
    </View>
  );
};

export default ProfileExperience;
