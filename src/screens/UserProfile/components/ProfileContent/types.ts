/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { PostExternalClientI } from '@/src/networks/content/types';
import { FetchProfileResultI } from '@/src/networks/profile/types';

export interface Meta {
  educationCount: number;
  statutoryCertCount: number;
  valueAddedCertCount: number;
  identityCount: number;
  visaCount: number;
  maritimeSkillsTotal: number;
}

export interface Request {
  status: 'PENDING' | 'CONNECTED' | null;
  senderRequestId: string;
}
export interface ProfileContentProps {
  data: FetchProfileResultI;
  isUserProfile: boolean;
  aboutProfile: AboutProfileI | null;
}

export type AboutProfileI = {
  profileId: string;
  username: string;
  name: string;
  avatar: string;
  designationText: string;
  designationAlternativeId: string;
  designationRawDataId: null;
  entityText: string;
  entityId: string;
  entityRawDataId: null;
  followersCount: number;
  followingsCount: number;
  description: null;
  isFollowing: boolean;
  request: Request;
  meta: Meta;
  institutions: null;
  identities: null;
  statutoryCerts: null;
  mutuals: { avatar: string }[];
  maritimeSkills: null;
};

export interface UseProfileContentResult {
  profilePosts: PostExternalClientI[];
  refreshing: boolean;
  loading: boolean;
  following: boolean;
  setFollowing: React.Dispatch<React.SetStateAction<boolean>>;
  handleFollow: () => Promise<boolean | undefined>;
  handleRefresh: () => Promise<void>;
  handleLoadMore: () => Promise<void>;
  handleLikePost: (post: PostExternalClientI, reactionType?: string) => Promise<void>;
  handleDeletePost: (post: PostExternalClientI) => Promise<void>;
}
