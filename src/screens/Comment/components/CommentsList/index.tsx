import React, { useEffect, useImperativeHandle, forwardRef, useState } from 'react';
import { ActivityIndicator, Text, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { CommentItemI } from '@/src/networks/content/types';
import { ScrapBookCommentFetchForClientI } from '@/src/networks/port/types';
import CommentItem from '../CommentItem';
import CommentsHead from '../CommentsHead';
import { CommentsListProps, CommentsListRef } from './types';
import { useCommentsListResult } from './useHook';

type UnifiedComment = CommentItemI | ScrapBookCommentFetchForClientI;

const normalizeComment = (
  comment: UnifiedComment,
  type: 'USER_POST' | 'SCRAPBOOK_POST',
): CommentItemI => {
  if (type === 'SCRAPBOOK_POST') {
    const scrapbookComment = comment as ScrapBookCommentFetchForClientI;
    return {
      id: scrapbookComment.id,
      text: scrapbookComment.text,
      createdAt:
        typeof scrapbookComment.createdAt === 'string'
          ? scrapbookComment.createdAt
          : scrapbookComment.createdAt.toISOString(),
      updatedAt:
        typeof scrapbookComment.updatedAt === 'string'
          ? scrapbookComment.updatedAt
          : scrapbookComment.updatedAt.toISOString(),
      cursorId: scrapbookComment.cursorId || 0,
      Profile: {
        id: scrapbookComment.Profile.id,
        name: scrapbookComment.Profile.name,
        avatar: scrapbookComment.Profile.avatar,
        designation: scrapbookComment.Profile.designation,
        entity: scrapbookComment.Profile.entity,
      },
      replies: scrapbookComment.replies?.map((reply) => normalizeComment(reply, type)) || [],
      repliesCount: scrapbookComment.repliesCount || 0,
    };
  }
  return comment as CommentItemI;
};

const CommentsList = forwardRef<CommentsListRef, CommentsListProps>(
  ({ postId, onReplyPress, type, portUnLocode }, ref) => {
    const [postLoaded, setPostLoaded] = useState(false);
    const { comments, loading, handleLoadMore, fetchComments, hasMore } = useCommentsListResult(
      postId,
      type,
    );
    const flatListRef = React.useRef<FlatList>(null);

    useEffect(() => {
      if (postLoaded) {
        fetchComments();
      }
    }, [postId, postLoaded]);

    const handlePostLoaded = () => {
      setPostLoaded(true);
    };

    useImperativeHandle(ref, () => ({
      scrollToTop: () => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      },
    }));

    const normalizedComments: CommentItemI[] = !comments?.comments
      ? []
      : comments.comments.map((comment) => normalizeComment(comment as UnifiedComment, type));

    const renderEmptyComponent = () => {
      if (!postLoaded) {
        return null;
      }

      if (loading) {
        return (
          <View className="p-4 items-center">
            <ActivityIndicator size="small" color="#448600" />
            <Text className="text-gray-600 mt-2">Loading comments...</Text>
          </View>
        );
      }

      return (
        <View className="p-4 items-center">
          <Text className="text-labelGray">No comments yet</Text>
          <Text className="text-gray-400 text-sm mt-1">Be the first to comment!</Text>
        </View>
      );
    };

    const renderItem = ({ item }: { item: CommentItemI }) => (
      <CommentItem item={item} postId={postId} onReplyPress={onReplyPress} type={type} level={0} />
    );

    const onEndReached = () => {
      if (!loading && postLoaded && hasMore) {
        handleLoadMore();
      }
    };

    return (
      <View className="flex-1">
        <FlatList
          ref={flatListRef}
          data={normalizedComments}
          keyExtractor={(item: CommentItemI) => item.id}
          renderItem={renderItem}
          showsVerticalScrollIndicator={false}
          onEndReached={onEndReached}
          initialNumToRender={10}
          onEndReachedThreshold={0.8}
          contentContainerStyle={{ paddingBottom: 20 }}
          ListHeaderComponent={
            <CommentsHead
              type={type}
              postId={postId}
              onPostLoaded={handlePostLoaded}
              portUnLocode={portUnLocode}
            />
          }
          ListEmptyComponent={renderEmptyComponent}
          scrollEnabled={true}
        />
      </View>
    );
  },
);

export default CommentsList;
