import { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Text,
  View,
  ListRenderItem,
  RefreshControl,
} from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { showToast } from '@/src/utilities/toast';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import { PortVisitorFetchResultI } from '@/src/networks/experience/types';
import { fetchPortVisits } from '@/src/networks/experience/visitedPorts';

const PAGE_SIZE = 10;

const PortsVisitedScreen = () => {
  const route = useRoute<RouteProp<ProfileStackParamsListI, 'PortsVisited'>>();
  const navigation = useNavigation();
  const { profileId } = route.params as { profileId: string };

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<PortVisitorFetchResultI[]>([]);
  const [page, setPage] = useState(0);
  const [total, setTotal] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const onBack = () => navigation.goBack();

  const fetchData = async (pageNumber: number, isRefresh = false) => {
    try {
      if (isRefresh) setRefreshing(true);
      else if (pageNumber > 0) setIsLoadingMore(true);
      else setLoading(true);

      const response = await fetchPortVisits({
        profileId,
        page: pageNumber,
        pageSize: PAGE_SIZE,
      });

      setTotal(response.total);
      if (isRefresh) setData(response.data);
      else setData((prev) => [...prev, ...response.data]);
      setPage(pageNumber);
    } catch (err) {
      if (!data.length && !isRefresh && pageNumber === 0) {
        triggerErrorBoundary(new Error('Failed to load ports visited'));
      } else {
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to fetch data',
        });
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
      setIsLoadingMore(false);
    }
  };

  useEffect(() => {
    fetchData(0);
  }, []);

  const onRefresh = () => {
    fetchData(0, true);
  };

  const loadMore = () => {
    if (data.length < total && !isLoadingMore) {
      fetchData(page + 1);
    }
  };

  const renderItem: ListRenderItem<PortVisitorFetchResultI> = ({ item }) => (
    <View className="flex-row justify-between px-4 py-3 border-b border-gray-200">
      <Text className="text-base text-black">{item.name}</Text>
      <Text className="text-base text-black">{item.country?.name || '—'}</Text>
    </View>
  );

  if (loading && !refreshing) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <SafeArea>
      <View className="flex-1">
        <View className="flex-row items-center px-4">
          <BackButton onBack={onBack} label="Ports Visited" labelClassname="text-xl font-medium" />
        </View>
        <View className="flex-row justify-between px-4 py-3 border-b border-gray-200">
          <Text className="font-medium text-sm">Port Name</Text>
          <Text className="font-medium text-sm">Country</Text>
        </View>
        <FlatList
          data={data}
          keyExtractor={(_, index) => index.toString()}
          renderItem={renderItem}
          onEndReached={loadMore}
          onEndReachedThreshold={0.7}
          ListFooterComponent={
            isLoadingMore ? <ActivityIndicator size="small" className="my-2" /> : null
          }
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        />
      </View>
    </SafeArea>
  );
};

export default PortsVisitedScreen;
