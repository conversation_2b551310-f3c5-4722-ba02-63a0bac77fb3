import type { Image } from 'react-native-image-crop-picker';
import type { UseFormReturn } from 'react-hook-form';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export interface EditUserProfileFormDataI {
  name: string;
  designation: Omit<SearchResultI, 'name'>;
  entity: Omit<SearchResultI, 'name'>;
  description: string;
  avatar?: Image | string | null;
}

export interface UseEditUserProfileFormI {
  methods: UseFormReturn<EditUserProfileFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: EditUserProfileFormDataI) => void;
  handleAvatarChange: (image: Image) => void;
  handleAvatarDelete: () => void;
  avatarFile: Image | null;
  isAvatarDeleted: boolean;
}

export interface EditUserProfileFormPropsI {
  onBack: () => void;
}

export interface PostMediaI {
  fileUrl: string;
  caption: string | null;
  extension: string;
}

export interface PresignedUrlResponseI {
  uploadUrl: string;
  accessUrl: string;
}
