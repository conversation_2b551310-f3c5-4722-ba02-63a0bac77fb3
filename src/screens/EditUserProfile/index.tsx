/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { BottomTabNavigationI } from '@/src/navigation/types';
import EditUserProfileForm from './components/EditUserProfileForm';

const EditUserProfile = () => {
  const navigation = useNavigation<BottomTabNavigationI>();
  return (
    <SafeArea>
      <ScrollView>
        <EditUserProfileForm onBack={() => navigation.goBack()} />
      </ScrollView>
    </SafeArea>
  );
};

export default EditUserProfile;
