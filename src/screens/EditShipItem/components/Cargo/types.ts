import { ShipCreateEditPayloadI } from '../EditShipItem/types';

export type ShipCargoI = {
  id: string;
  name: string;
  description: string;
  fromDate: string;
  toDate: string;
};

export type CargoPropsI = {
  profileId: string;
  shipId: string;
  isAddVisible: boolean;
  handleAdd: () => ShipCreateEditPayloadI[];
  fromProfileExperience: boolean;
  shipData: {
    fromDate: string;
    toDate: string | null;
  };
};

export type UseCargoI = {
  handleAdd: () => void;
};
