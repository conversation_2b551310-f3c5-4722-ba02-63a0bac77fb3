import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditShipItem from './components/EditShipItem';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditShipItem'>;

const EditShipItemScreen = () => {
  const route = useRoute<RouteProps>();
  const { data, field, shipId, fromProfileExperience, refetch } = route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <EditShipItem
        onBack={navigation.goBack}
        profileId={'profileId'}
        shipId={shipId}
        preFilledData={data!}
        field={field!}
        fromProfileExperience={fromProfileExperience!}
        refetch={refetch!}
      />
    </SafeArea>
  );
};

export default EditShipItemScreen;
