/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp, useRoute } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import SearchScreenForm from './components/SearchScreen';

type RouteProps = RouteProp<ProfileStackParamsListI, 'SearchScreen'>;

const EntitySearchScreen = () => {
  const route = useRoute<RouteProps>();
  const { title, placeholder, selectionKey, multipleSelection } = route.params;

  return (
    <SafeArea>
      <SearchScreenForm
        title={title}
        placeholder={placeholder}
        selectionKey={selectionKey}
        multipleSelection={multipleSelection!}
      />
    </SafeArea>
  );
};

export default EntitySearchScreen;
