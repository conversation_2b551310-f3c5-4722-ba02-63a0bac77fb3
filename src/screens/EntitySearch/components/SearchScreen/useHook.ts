import { useRef, useState, useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import {
  addToMultipleSelection,
  setMultipleSelections,
  setSelection,
} from '@/src/redux/slices/entitysearch/searchSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { debounceAsync } from '@/src/utilities/search/debounce';
import { showToast } from '@/src/utilities/toast';
import { AppStackParamListI } from '@/src/navigation/types';
import { optionAddAPI } from '@/src/networks/entitySearch/optionAdd';
import { optionFetchSearchAPI } from '@/src/networks/entitySearch/optionSearch';
import { CERTIFICATE_TYPES_ROUTE_MAP } from '../collectionMap';
import { SearchResponseI, SearchResultI } from './types';

const useSearchScreen = (selectionKey: string, multipleSelection: boolean) => {
  const [text, setText] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<SearchResultI[]>([]);
  const [currentPage, setCurrentPage] = useState('0');
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState<string | null>(null);

  const PAGE_SIZE = 10;
  const [collectionType, setCollectionType] = useState<string | null>(null);
  const [entityWebsite, setEntityWebsite] = useState<string | null>(null);
  const [selectionMap, setSelectionMap] = useState<Map<string, SearchResultI>>(new Map());
  const dispatchForMultipleSelection = useDispatch();

  const [collection, typeKey] = selectionKey.split('/');
  const type = typeKey ? CERTIFICATE_TYPES_ROUTE_MAP[typeKey.toLowerCase()] : undefined;

  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();

  const currentSelection = useSelector(selectSelectionByKey(collection));

  const fetchOptions = async (
    collection: string,
    searchText: string,
    page: string,
    type?: string,
  ) => {
    try {
      return await optionFetchSearchAPI(collection, searchText, page, type);
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Search Failed',
            description: 'Unable to fetch search results',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
      return [];
    }
  };

  const debouncedFetchOptions = useRef(
    debounceAsync(fetchOptions as (...args: unknown[]) => Promise<unknown>, 300),
  ).current;

  useEffect(() => {
    const performSearch = async () => {
      if (!searchQuery || searchQuery.length < 2) {
        setOptions([]);
        return;
      }

      setLoading(true);
      try {
        const { data } = (await debouncedFetchOptions(
          collection,
          searchQuery,
          '0',
          type,
        )) as SearchResponseI;

        if (collection.toLowerCase() === 'designation') {
          setOptions(data.filter((item) => item.id !== '9a6f9c02-1b6c-4a13-8f7d-9e2c6d4f4a92'));
        } else {
          setOptions(data);
        }
        setCurrentPage('0');
        setHasMore(data.length === PAGE_SIZE);
      } catch (error) {
        handleError(error, {
          handle4xxError: () => {
            showToast({
              message: 'Search Failed',
              description: 'Unable to fetch search results',
              type: 'error',
            });
          },
          handle5xxError: () => {
            showToast({
              message: 'Server Error',
              description: 'Please try again later',
              type: 'error',
            });
          },
        });
      } finally {
        setLoading(false);
      }
    };

    performSearch();
  }, [searchQuery, collection]);

  useEffect(() => {
    if (currentSelection && !multipleSelection) {
      const isAlreadyInOptions = options.some((option) => option.id === currentSelection.id);

      if (!isAlreadyInOptions) {
        setOptions((prevOptions) => [currentSelection, ...prevOptions]);
      }
    }
  }, [currentSelection, options, multipleSelection]);

  const addOption = async (
    collection: string,
    optionText: string,
    type?: 'EDUCATION' | 'COMPANY' | 'STATUTORY' | 'VALUE_ADDED' | 'MARITIME' | 'OTHER',
    website?: string,
  ) => {
    try {
      let payload: {
        name: string;
        type?: 'EDUCATION' | 'COMPANY' | 'STATUTORY' | 'VALUE_ADDED' | 'MARITIME' | 'OTHER';
        website?: string;
        category?: string;
      };
      switch (collection) {
        case 'entity':
          payload = { name: optionText, type };
          if (website) {
            payload.website = website;
          }
          break;
        case 'certificate-course':
          payload = { name: optionText, type };
          break;
        case 'skill':
          payload = { name: optionText, category: type };
          break;
        default:
          payload = { name: optionText };
          break;
      }

      return await optionAddAPI(collection, payload);
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Add Failed',
            description: `Unable to add new ${collection}`,
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
      throw error;
    }
  };

  const handleBack = () => {
    if (multipleSelection) {
      dispatchForMultipleSelection(
        setMultipleSelections({ key: collection, value: Array.from(selectionMap.values()) }),
      );
    }
    navigation.goBack();
  };

  const handleAddOption = async (dispatch: AppDispatch) => {
    if (!text) return;

    setLoading(true);
    try {
      const newOption = await addOption(
        collection,
        text,
        collectionType as
          | 'EDUCATION'
          | 'COMPANY'
          | 'STATUTORY'
          | 'VALUE_ADDED'
          | 'MARITIME'
          | 'OTHER',
        entityWebsite as string,
      );

      if (collection.toLowerCase() === 'skill' || collection.toLowerCase() === 'fueltype') {
        dispatch(addToMultipleSelection({ key: collection, value: newOption }));
      } else {
        dispatch(setSelection({ key: collection, value: newOption }));
      }
      setIsVisible(false);
      navigation.goBack();
      showToast({
        message: 'Success',
        description: `Added new ${collection.startsWith('designation-') ? 'designation' : collection} successfully`,
        type: 'success',
      });
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Add Failed',
            description: `Unable to add new ${collection.startsWith('designation-') ? 'designation' : collection}`,
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTextChange = async (inputText: string) => {
    setText(inputText);

    if (!inputText) {
      setSearchQuery(null);
      setOptions([]);
      return;
    }

    setSearchQuery(inputText);
    if (inputText.length < 2) {
      setOptions([...options]);
      return;
    }

    setCurrentPage('0');

    setLoading(true);
    try {
      const { data } = (await debouncedFetchOptions(
        collection,
        inputText,
        currentPage,
        type,
      )) as SearchResponseI;
      if (collection.toLowerCase() === 'designation') {
        setOptions(data.filter((item) => item.id !== '9a6f9c02-1b6c-4a13-8f7d-9e2c6d4f4a92'));
      } else {
        setOptions(data);
      }
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Search Failed',
            description: 'Unable to fetch search results',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSetOption = (item: SearchResultI, dispatch: AppDispatch) => {
    setText(item.name);
    setIsOpen(false);

    if (multipleSelection) {
      setSelectionMap((prevMap) => {
        if (prevMap.has(item.id)) return prevMap;
        const newMap = new Map(prevMap);
        newMap.set(item.id, item);
        return newMap;
      });
    } else {
      dispatch(setSelection({ key: collection, value: item }));
      navigation.goBack();
    }
  };

  const handleSearchOpen = () => {
    setIsOpen(true);
  };

  const toggleVisible = () => {
    setIsVisible(!isVisible);
  };

  const loadMoreOptions = async () => {
    if (loading || !text?.trim() || !hasMore) return;

    if (options.length < PAGE_SIZE) return;

    setLoading(true);
    try {
      const nextPage = (parseInt(currentPage, 10) + 1).toString();
      const { data } = (await debouncedFetchOptions(
        collection,
        text,
        nextPage,
        type,
      )) as SearchResponseI;

      if (data.length > 0) {
        setOptions((prev) => [...prev, ...data]);
        setCurrentPage(nextPage);
        if (data.length < PAGE_SIZE) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Load More Failed',
            description: 'Unable to load more results',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeSelectOption = (itemId: string) => {
    setSelectionMap((prevMap) => {
      const newMap = new Map(prevMap);
      newMap.delete(itemId);
      return newMap;
    });
  };

  return {
    text,
    setText,
    isOpen,
    setIsOpen,
    isVisible,
    loading,
    options,
    handleBack,
    handleAddOption,
    handleTextChange,
    handleSetOption,
    handleSearchOpen,
    toggleVisible,
    loadMoreOptions,
    setCollectionType,
    setEntityWebsite,
    collection,
    selectionMap,
    handleDeSelectOption,
  };
};

export default useSearchScreen;
