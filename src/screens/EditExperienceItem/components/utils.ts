import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  ExperienceDesignationI,
  ExperienceFetchForClientResultI,
  ExperienceItemI,
  ExperienceModuleCreateOneParamsI,
  ExperienceModuleUpdateOneParamsI,
  OprTypeE,
} from '@/src/redux/slices/experience/types';
import { DesignationWithDateI, FieldTypeI } from './EditExperienceItem/types';

export const toMonthYear = (dateStr: string): string => {
  const date = new Date(dateStr);
  const options: Intl.DateTimeFormatOptions = {
    month: 'short',
    year: 'numeric',
  };
  return date.toLocaleDateString('en-US', options);
};

export const generateAddExperiencePayload = (
  entity: SearchResultI,
  designations: (SearchResultI & { fromDate: string; toDate?: string })[],
) => {
  const payload: ExperienceModuleCreateOneParamsI[] = [
    {
      opr: 'CREATE',
      entity: entity,
      designations:
        designations?.map((item) => {
          return {
            opr: 'CREATE',
            fromDate: item.fromDate,
            toDate: item.toDate,
            designation: {
              ...item,
              opr: 'CREATE',
            },
          };
        }) || [],
    },
  ];
  return payload;
};

export const generateEditExperiencePayload = (
  experience: ExperienceItemI,
  selectedExperience: ExperienceFetchForClientResultI | undefined,
) => {
  const existingDesignations = selectedExperience?.designations || [];
  const currentDesignations = experience.designations || [];
  const payload: ExperienceModuleUpdateOneParamsI = [
    {
      opr: 'UPDATE',
      id: experience.id,
      entity: {
        id: experience.entity.id,
        dataType: experience.entity.dataType,
      },
      designations: [
        ...currentDesignations.flatMap((item: ExperienceDesignationI) => {
          const existingItem = existingDesignations.find(
            (d) => d.designation.id === item.designation.id,
          );

          const isCreateDuplicate = existingItem && !item.id;

          if (!existingItem || isCreateDuplicate) {
            return [
              {
                opr: 'CREATE' as OprTypeE | undefined,
                id: item?.experienceDesignationId,
                fromDate: item.fromDate,
                toDate: item.toDate,
                designation: {
                  id: item.designation.id,
                  dataType: item.designation.dataType,
                },
              },
            ];
          }

          const hasChanged =
            item.fromDate !== existingItem.fromDate || item.toDate !== existingItem.toDate;

          if (hasChanged) {
            return [
              {
                opr: 'UPDATE' as OprTypeE | undefined,
                id: item?.id,
                fromDate: item.fromDate,
                toDate: item.toDate,
                designation: {
                  id: item.designation.id,
                  dataType: item.designation.dataType,
                },
              },
            ];
          }

          return [];
        }),

        ...existingDesignations
          .filter(
            (existing) =>
              !currentDesignations.some((d) => d.designation.id === existing.designation.id),
          )
          .map((deleted) => ({
            opr: 'DELETE' as const,
            id: deleted.id,
          })),
      ],
    },
  ];
  return payload;
};

export const generateExperiencePayload = (
  localEntity: SearchResultI,
  initialEntity: SearchResultI,
  localDesignations: DesignationWithDateI[],
  initialDesignations: DesignationWithDateI[],
  experienceId?: string,
  field?: FieldTypeI,
) => {
  const experience = {
    opr: experienceId ? 'UPDATE' : 'CREATE',
  };

  if (experienceId) {
    experience['id'] = experienceId;
  }

  if (localEntity.id !== initialEntity?.id) {
    experience['entity'] = localEntity;
  } else {
    experience['opr'] = 'NESTED_OPR';
  }

  const result = [];
  let fieldDesignationHandled = false;

  if (initialDesignations.length > 0) {
    initialDesignations.forEach((iDesignation) => {
      if (iDesignation.id !== undefined) {
        const id = iDesignation.id;
        const matchingLocal = localDesignations.find((lDesignation) => lDesignation.id === id);

        if (!matchingLocal) {
          result.push({
            id: id,
            opr: 'DELETE',
          });
        } else {
          const fieldsChanged =
            iDesignation.designation !== matchingLocal.designation ||
            iDesignation.fromDate !== matchingLocal.fromDate ||
            iDesignation.toDate !== matchingLocal.toDate;

          if (fieldsChanged) {
            if (field?.id && id === field.id) {
              fieldDesignationHandled = true;
            }
            result.push({
              id: id,
              opr: 'UPDATE',
              designation: matchingLocal.designation,
              fromDate: matchingLocal.fromDate,
              toDate: matchingLocal.toDate,
            });
          }
        }
      }
    });
  }

  localDesignations.forEach((lDesignation) => {
    if (!lDesignation.id) {
      result.push({
        opr: 'CREATE',
        designation: lDesignation.designation,
        fromDate: lDesignation.fromDate,
        toDate: lDesignation.toDate,
      });
    }
  });

  if (field?.id && !fieldDesignationHandled) {
    const fieldDesignation = localDesignations.find((d) => d.id === field.id);
    if (fieldDesignation) {
      result.push({
        id: field.id,
        opr: 'NESTED_OPR',
      });
    }
  }

  experience['designations'] = result;

  return [experience];
};
