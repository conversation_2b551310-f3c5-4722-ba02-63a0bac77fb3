/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Dispatch, SetStateAction } from 'react';
import { FieldArrayWithId, UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ExperienceDesignationI } from '@/src/redux/slices/experience/types';

export interface EditExperienceItemPropsI {
  onBack: () => void;
  profileId: string;
  experienceId?: string;
}

export type DesignationsI = {
  indexKey: string;
  id: string;
  name: string;
  dataType: 'raw' | 'master';
};

export type UseEditExperienceItemI = {
  methods: UseFormReturn<ExperienceFormDataI, any, ExperienceFormDataI>;
  fields: FieldArrayWithId<ExperienceFormDataI, 'designations', 'id'>[];
  company: SearchResultI;
  fetchedCompany: SearchResultI;
  loading: boolean;
  isSubmitting: boolean;
  onSubmit: (data: ExperienceFormDataI) => Promise<void>;
  designations: DesignationsI[];
  fetchedDesignations: ExperienceDesignationI[] | [];
  handleAddDesignation: () => void;
  handleRemoveDesignation: (index: number) => void;
  // handleAddEditShip: (field: FieldTypeI, shipId?: string) => void;
  updatedFields: ExperienceDesignationI[];
  // handleDeleteShip: (field: FieldTypeI, shipId: string) => void;
  isDeleting: boolean;
  setLocalDesignations: Dispatch<SetStateAction<DesignationWithDateI[]>>;
  localDesignations: DesignationWithDateI[];
};

export interface ExperienceFormDataI {
  company: SearchResultI;
  designations: ExperienceDesignationI[] | [];
  fromDate?: string;
  toDate?: string;
}

export type apiResponseTypeI = {
  entity: SearchResultI;
  designations: {
    id: string;
    fromDate: string;
    toDate: string;
    designation: SearchResultI;
    ships: {
      name: string;
      ship: Omit<SearchResultI, 'id'> & { imo: string };
      subVesselType: SearchResultI;
      fromDate: string;
      toDate: string | null;
    }[];
  }[];
};

export type FetchedExperienceI = {
  company: SearchResultI;
  designations: {
    id: string;
    fromDate: string;
    toDate: string;
    designation: SearchResultI;
    ships: {
      name: string;
      ship: Omit<SearchResultI, 'id'> & { imo: string };
      subVesselType: SearchResultI;
      fromDate: string;
      toDate: string | null;
    }[];
  }[];
};

export type FieldTypeI = {
  designation: SearchResultI;
  fromDate: string;
  id: string;
  ships: FieldShipType[];
  toDate: string;
};

export type FieldShipType = {
  fromDate: string;
  id: string;
  name: string;
  ship: Omit<SearchResultI, 'id'> & { imo: string };
  subVesselType: SearchResultI;
  toDate: string;
};

export type DesignationWithDateI = {
  designation: {
    id?: string;
    name?: string;
    dataType?: 'raw' | 'master';
  };
  fromDate?: string;
  toDate?: string;
  id?: string;
  ships: any[];
};
