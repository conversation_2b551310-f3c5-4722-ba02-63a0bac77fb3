import { z } from 'zod';

const getTodayString = () => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

export const EntitySchema = z.object({
  id: z.string().optional(),
  dataType: z.string().optional(),
});

const DesignationSchema = z
  .object({
    id: z.string().optional(),
    designation: z.any().nullable(),
    name: z.string().optional(),
    experienceDesignationId: z.string().optional(),
    fromDate: z.string().nonempty('From Date is required'),
    toDate: z.string().nonempty('To Date is required'),
  })
  .refine(
    (data) => {
      if (!data.fromDate || !data.toDate) return true;

      return new Date(data.fromDate) < new Date(data.toDate);
    },
    {
      message: 'From date must be earlier than To date',
      path: ['fromDate'],
    },
  )
  .refine(
    (data) => {
      if (!data.toDate) return true;

      const today = new Date(getTodayString());

      return new Date(data.toDate) <= today;
    },
    {
      message: 'To date cannot be in the future',
      path: ['toDate'],
    },
  );

export const editExperienceSchema = z.object({
  company: EntitySchema.optional(),
  designations: z.array(DesignationSchema).optional(),
});

export type ExperienceFormDataI = z.infer<typeof editExperienceSchema>;
