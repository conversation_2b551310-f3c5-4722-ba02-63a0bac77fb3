import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { deleteVisaDocument, fetchVisaDocuments } from '@/src/redux/slices/about/aboutSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { navigate } from '@/src/utilities/navigation';
import { showToast } from '@/src/utilities/toast';
import { deleteDocumentAPI } from '@/src/networks/career/document';

export const useEditVisaDocument = (profileId: string) => {
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const [deleteDocumentId, setDeleteDocumentId] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const navigation = useNavigation();

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  useEffect(() => {
    const fetchInitialDocuments = async () => {
      setLoading(true);
      try {
        await dispatch(fetchVisaDocuments({ profileId, page: 0 })).unwrap();
      } catch (error) {
        triggerErrorBoundary(
          new Error(
            'Failed to load visa documents: ' +
              (error instanceof Error ? error.message : 'Unknown error'),
          ),
        );
      } finally {
        setLoading(false);
      }
    };
    fetchInitialDocuments();
  }, []);

  const onEditDocument = (documentId: string) => {
    navigate('EditDocumentItem', {
      profileId,
      documentId,
      type: 'visa',
    });
  };

  const onDeleteDocument = async () => {
    setIsDeleting(true);
    try {
      await deleteDocumentAPI(deleteDocumentId, 'visa');
      dispatch(deleteVisaDocument(deleteDocumentId));
      showToast({
        message: 'Success',
        description: 'Document deleted successfully',
        type: 'success',
      });
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Error',
            description: 'Failed to Delete Document',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    onEditDocument,
    onDeleteDocument,
    isVisible,
    setIsVisible,
    setDeleteDocumentId,
    isDeleting,
    loading,
  };
};
