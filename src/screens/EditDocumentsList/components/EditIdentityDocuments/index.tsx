import { ActivityIndicator, FlatList, Pressable, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import { selectIdentityDocuments } from '@/src/redux/selectors/about';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { IdentityI } from '../EditDocumentationList/types';
import { findValidity } from '../utils/findValidity';
import { useEditIdentityDocument } from './useHook';

export const EditIdentityDocuments = ({
  profileId,
  editable,
}: {
  profileId: string;
  editable: boolean;
}) => {
  const documents = useSelector(selectIdentityDocuments);
  const {
    onEditDocument,
    onDeleteDocument,
    // loadMoreDocuments,
    hasMore,
    isVisible,
    setIsVisible,
    setDeleteDocumentId,
    isDeleting,
    loading,
  } = useEditIdentityDocument(profileId);

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  const renderItem = ({ item, index }: { item: IdentityI; index: number }) => {
    const isLast = index === documents.length - 1;
    const expiry = findValidity(item.untilDate, !item.type);
    const isExpired = expiry.length === 0;
    const textColor = isExpired ? 'text-gray-400' : 'text-black';

    const expiryText =
      expiry.length > 0 ? `${expiry[0]} ${expiry[1] === 'M' ? 'month(s)' : 'year(s)'}` : 'Expired';

    return (
      <View className={`py-4 ${isLast ? '' : 'border-b border-[#E5E5E5]'}`}>
        <Text className={`text-base font-medium ${textColor}`}>{item.type}</Text>
        <View className="flex-row justify-between mt-2 items-center">
          <Text className={`text-sm mt-1 ${textColor}`}>
            {item.country.name} | {expiryText}
          </Text>
          {editable && (
            <View className="flex-row gap-1 items-center">
              <Pressable onPress={() => onEditDocument(item.id)} className="p-2">
                <EditPencil />
              </Pressable>
              <Pressable
                onPress={() => {
                  setDeleteDocumentId(item.id);
                  setIsVisible(true);
                }}
                className="p-2"
              >
                <DeleteIcon />
              </Pressable>
            </View>
          )}
        </View>
      </View>
    );
  };

  return documents.length === 0 ? (
    <NotFound />
  ) : (
    <>
      <FlatList
        data={documents}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        //   onEndReached={loadMoreDocuments}
        //   onEndReachedThreshold={0.5}
        showsVerticalScrollIndicator={false}
        //   ListFooterComponent={
        //     hasMore ? (
        //       <View className="py-4">
        //         <ActivityIndicator size="small" color="#448600" />
        //       </View>
        //     ) : null
        //   }
      />
      <CustomModal
        isVisible={isVisible}
        onCancel={() => setIsVisible(false)}
        title="Are you sure you want to delete this document?"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={onDeleteDocument}
        isConfirming={isDeleting}
      />
    </>
  );
};
