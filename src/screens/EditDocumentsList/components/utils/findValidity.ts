export const findValidity = (date: string, showYear: boolean): [number, 'Y' | 'M' | 'D'] | [] => {
  const today = new Date();
  const validityDate = new Date(date); // Assumes ISO string (e.g. 2027-07-12)

  if (validityDate <= today) return [];

  const yearDiff = validityDate.getFullYear() - today.getFullYear();
  const monthDiff = validityDate.getMonth() - today.getMonth();
  const totalMonths = yearDiff * 12 + monthDiff;

  const diffTime = validityDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (showYear) {
    const fullYears = Math.floor(totalMonths / 12);
    if (fullYears >= 1) {
      return [fullYears, 'Y'];
    } else {
      return [totalMonths, 'M'];
    }
  } else {
    return totalMonths >= 1 ? [totalMonths, 'M'] : [diffDays, 'D'];
  }
};
