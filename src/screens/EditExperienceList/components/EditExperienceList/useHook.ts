/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useCallback, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { ExperienceFetchForClientResultI } from '@/src/redux/slices/experience/types';
import { showToast } from '@/src/utilities/toast';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import { deleteExperience, fetchAllExperiences } from '@/src/networks/experience/experience';
import { UseEditExperienceListI } from './types';

const useEditExperienceList = (profileId: string): UseEditExperienceListI => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [experiences, setExperiences] = useState<ExperienceFetchForClientResultI[]>([]);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  const fetchExperiences = async () => {
    if (isLoading) return;
    setIsLoading(true);
    try {
      const response = await fetchAllExperiences(profileId);
      setExperiences(response.data);
    } catch (error) {
      triggerErrorBoundary(
        new Error(
          'Failed to load experiences: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setIsLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchExperiences();
    }, [profileId]),
  );

  const handleModal = () => {
    setIsModalVisible(!isModalVisible);
  };

  const onAddExperience = () => {
    navigation.navigate('EditExperienceItem', { profileId });
  };

  const onEditExperience = (experienceId: string) => {
    navigation.navigate('EditExperienceItem', { experienceId, profileId });
  };

  const onDeleteExperience = async (experienceId: string) => {
    setIsLoading(true);
    try {
      await deleteExperience([{ id: experienceId, opr: 'DELETE' }]);
      setExperiences((prev) => prev.filter((item) => item.id !== experienceId));
      showToast({
        message: 'Success',
        description: 'Experience deleted successfully',
        type: 'success',
      });
    } catch (err) {
      const errorMsg = 'Error deleting experience';
      showToast({
        message: 'Error',
        description: errorMsg,
        type: 'error',
      });
    } finally {
      handleModal();
      setIsLoading(false);
    }
  };

  return {
    experiences,
    isLoading,
    isModalVisible,
    handleModal,
    onAddExperience,
    onEditExperience,
    onDeleteExperience,
    navigation,
  };
};

export default useEditExperienceList;
