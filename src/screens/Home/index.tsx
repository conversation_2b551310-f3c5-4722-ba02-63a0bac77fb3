/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useEffect } from 'react';
import { StatusBar, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { fetchAndSaveUserProfile } from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { BottomTabNavigationI } from '@/src/navigation/types';
import UserPostList from './components/PostList';
import TopBar from './components/TopBar';

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);

  const handleSearchPress = () => {
    navigation.navigate('HomeStack', {
      screen: 'GlobalSearch',
    });
  };
  const handleMessagePress = () => {
    navigation.navigate('HomeStack', {
      screen: 'Chats',
      params: { profileId: currentUser.profileId },
    });
  };

  const fetchCurrentUserProfile = async () => {
    try {
      if (currentUser?.profileId) {
        await dispatch(fetchAndSaveUserProfile({ id: currentUser.profileId }));
      }
    } catch (err) {
      handleError(err);
    }
  };

  useEffect(() => {
    fetchCurrentUserProfile();
  }, []);

  return (
    <SafeArea>
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="flex-1">
        <TopBar onSearchPress={handleSearchPress} onMessagePress={handleMessagePress} />
        <UserPostList />
      </View>
    </SafeArea>
  );
};

export default HomeScreen;
