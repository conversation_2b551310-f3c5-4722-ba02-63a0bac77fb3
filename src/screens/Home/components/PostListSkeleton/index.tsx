/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { FlatList, View } from 'react-native';
import ShimmerBox from '@/src/components/Shimmer';

const PostListSkeleton = () => {
  const renderItem = () => (
    <View className="mx-3 my-4">
      <View className="flex-row items-center">
        <ShimmerBox variant="sphere" width={50} height={50} />
        <View className="flex-1 ml-3">
          <ShimmerBox width={'80%'} height={20} borderRadius={4} className="mb-1" />
          <ShimmerBox width={'70%'} height={16} borderRadius={4} className="mb-1" />
          <ShimmerBox width={'30%'} height={14} borderRadius={4} />
        </View>
        <ShimmerBox width={24} height={24} borderRadius={4} />
      </View>
      <View className="mt-3 mb-2">
        <ShimmerBox width={'100%'} height={18} borderRadius={4} className="mb-1" />
        <ShimmerBox width={'100%'} height={18} borderRadius={4} className="mb-1" />
        <ShimmerBox width={'100%'} height={18} borderRadius={4} className="mb-1" />
        <ShimmerBox width={'80%'} height={18} borderRadius={4} className="mb-1" />
      </View>
      <ShimmerBox width={'100%'} height={200} borderRadius={8} className="my-2" />
      <View className="flex-row items-center justify-between mt-2">
        <View className="flex-row flex-1 gap-3">
          <View className="flex-row items-center">
            <ShimmerBox width={70} height={30} borderRadius={15} />
          </View>
          <View className="flex-row items-center">
            <ShimmerBox width={70} height={30} borderRadius={15} />
          </View>
        </View>
        <View className="flex-row items-center">
          <ShimmerBox width={70} height={30} borderRadius={15} />
        </View>
      </View>
    </View>
  );

  return (
    <View className="flex-1 bg-white">
      <FlatList
        data={Array.from({ length: 3 })}
        keyExtractor={(_, index) => index.toString()}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
      />
    </View>
  );
};

export default PostListSkeleton;
