/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ActivityIndicator, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { FlatList, RefreshControl } from 'react-native-gesture-handler';
import { useSelector } from 'react-redux';
import NotFound from '@/src/components/NotFound';
import UserPost from '@/src/components/UserPost';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { BottomTabNavigationI } from '@/src/navigation/types';
import { PostExternalClientI } from '@/src/networks/content/types';
import Skeleton from '../PostListSkeleton';
import { PostListProps, ListFooterProps } from './types';
import usePostList from './useHook';

const INITIAL_NUM_TO_RENDER = 5;
const END_REACHED_THRESHOLD = 0.5;
const WINDOW_SIZE = 5;

const ListFooter = ({ isLoading }: ListFooterProps) => {
  if (!isLoading) return null;
  return (
    <View className="py-4">
      <ActivityIndicator size="small" />
    </View>
  );
};

const PostList = ({
  posts: externalPosts,
  refreshing: externalRefreshing,
  loading: externalLoading,
  onRefresh: externalOnRefresh,
  onLoadMore: externalOnLoadMore,
}: PostListProps) => {
  const {
    posts: fetchedPosts,
    refreshing: fetchedRefreshing,
    loading: fetchedLoading,
    handleRefresh: fetchedOnRefresh,
    handleLoadMore: fetchedOnLoadMore,
    handleLikePost,
    handleDeletePost,
  } = usePostList();

  const currentUser = useSelector(selectCurrentUser);

  const data = externalPosts ?? fetchedPosts;
  const refreshing = externalRefreshing ?? fetchedRefreshing;
  const loading = externalLoading ?? fetchedLoading;
  const onRefresh = externalOnRefresh ?? fetchedOnRefresh;
  const onLoadMore = externalOnLoadMore ?? fetchedOnLoadMore;

  const handleEndReached = () => {
    if (!loading && !refreshing && onLoadMore) {
      onLoadMore();
    }
  };

  const navigation = useNavigation<BottomTabNavigationI>();

  const renderItem = ({ item }: { item: PostExternalClientI }) => (
    <UserPost
      post={item}
      onLikePress={() => handleLikePost(item, 'LIKE')}
      onDeletePress={() => handleDeletePost(item)}
      isOwnPost={currentUser?.profileId === item.Profile.id}
      onLikeCountPress={() =>
        navigation.navigate('HomeStack', {
          screen: 'Likes',
          params: { postId: item.id, type: 'USER_POST' },
        })
      }
      onEditPress={() => {
        navigation.navigate('CreateStack', {
          screen: 'CreateContent',
          params: { postId: item.id, editing: true, type: 'USER_POST' },
        });
      }}
      onCommentPress={() =>
        navigation.navigate('HomeStack', {
          screen: 'Comment',
          params: { postId: item.id, type: 'USER_POST' },
        })
      }
    />
  );

  const keyExtractor = (item: PostExternalClientI) => item.id;

  if (fetchedLoading && !data?.length && !externalPosts) {
    return <Skeleton />;
  }

  if (!data?.length) {
    return (
      <NotFound
        title="No posts found"
        subtitle="There are no posts to display at the moment. Check back later or create a new post."
      />
    );
  }

  return (
    <View className="flex-1">
      <FlatList
        data={data}
        renderItem={renderItem}
        scrollEnabled={true}
        keyExtractor={keyExtractor}
        showsVerticalScrollIndicator={false}
        initialNumToRender={INITIAL_NUM_TO_RENDER}
        maxToRenderPerBatch={INITIAL_NUM_TO_RENDER}
        windowSize={WINDOW_SIZE}
        removeClippedSubviews
        refreshControl={
          <RefreshControl enabled={true} refreshing={refreshing} onRefresh={onRefresh} />
        }
        onEndReached={handleEndReached}
        onEndReachedThreshold={END_REACHED_THRESHOLD}
        ListFooterComponent={<ListFooter isLoading={loading} />}
      />
    </View>
  );
};

export default PostList;
