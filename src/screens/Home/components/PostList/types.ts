/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { PostExternalClientI } from '@/src/networks/content/types';

export interface PostListProps {
  posts?: PostExternalClientI[];
  refreshing?: boolean;
  loading?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
}

export interface UsePostListResult {
  posts: PostExternalClientI[];
  refreshing: boolean;
  loading: boolean;
  handleRefresh: () => Promise<void>;
  handleLoadMore: () => Promise<void>;
  handleLikePost: (post: PostExternalClientI, reactionType: string) => Promise<void>;
  handleDeletePost: (post: PostExternalClientI) => Promise<void>;
}

export interface ListFooterProps {
  isLoading: boolean;
}
