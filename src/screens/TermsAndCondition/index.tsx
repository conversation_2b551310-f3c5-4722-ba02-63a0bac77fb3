import { View, Text, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';

const TermsOfServiceScreen = () => {
  const navigation = useNavigation();

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <View className="px-4 py-2">
          <BackButton onBack={handleBack} label="" />
        </View>

        <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false}>
          <View className="pb-8">
            <Text className="text-3xl font-bold text-gray-900 mb-2">Terms of Service</Text>
            <Text className="text-sm text-gray-500 mb-8">Last updated: June 2025</Text>

            <View className="gap-y-6">
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  Welcome to Navicater
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  These Terms of Service govern your use of the Navicater platform, a professional
                  networking service designed for maritime industry professionals. By using our
                  service, you agree to these terms.
                </Text>
              </View>

              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  Acceptance of Terms
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  By accessing or using Navicater, you agree to be bound by these Terms of Service
                  and our Privacy Policy. If you disagree with any part of these terms, you may not
                  access the service.
                </Text>
              </View>

              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">Governing Law</Text>
                <Text className="text-base text-gray-700 leading-6">
                  These terms are governed by the laws. Any disputes will be resolved in the courts.
                </Text>
              </View>

              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">Changes to Terms</Text>
                <Text className="text-base text-gray-700 leading-6">
                  We may update these terms from time to time. We will notify you of significant
                  changes through the app or via email. Continued use of the service after changes
                  constitutes acceptance of the new terms.
                </Text>
              </View>

              <View className="bg-green-50 rounded-xl p-4">
                <Text className="text-xl font-semibold text-green-800 mb-3">Questions?</Text>
                <Text className="text-base text-green-700 leading-6">
                  If you have any questions about these Terms of Service, please contact us:
                </Text>
                <Text className="text-base text-green-700 leading-6 mt-3">
                  Email: <EMAIL>{'\n'}
                  Address: Navicater Solutions
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </SafeArea>
  );
};

export default TermsOfServiceScreen;
