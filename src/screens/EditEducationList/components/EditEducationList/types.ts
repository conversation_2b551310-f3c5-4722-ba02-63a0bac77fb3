import { Dispatch, SetStateAction } from 'react';
import { StackNavigationProp } from '@react-navigation/stack';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ProfileStackParamsListI } from '@/src/navigation/types';

export interface UseEditEducationListI {
  isSubmitting: boolean;
  onAddEducation: () => void;
  onEditEducation: (certificationId: string) => void;
  onDeleteEducation: (inputValue?: string | undefined) => void;
  navigation: StackNavigationProp<ProfileStackParamsListI>;
  loading: boolean;
  isVisible: boolean;
  setIsVisible: Dispatch<SetStateAction<boolean>>;
  setDeleteEducationId: Dispatch<SetStateAction<string>>;
  isDeleting: boolean;
}

export interface EditEducationListPropsI {
  onBack: () => void;
  profileId: string;
}

export interface EducationI {
  createdAt?: string;
  degree: SearchResultI;
  entity: SearchResultI;
  fromDate: string;
  id: string;
  toDate: string;
}
