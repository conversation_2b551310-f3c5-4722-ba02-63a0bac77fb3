import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditEducationList from './components/EditEducationList';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditCertificationList'>;

const EditEducationListScreen = () => {
  const route = useRoute<RouteProps>();
  const { editable } = route.params;
  const { profileId } = useSelector(selectCurrentUser);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <EditEducationList profileId={profileId} onBack={navigation.goBack} editable={editable!} />
    </SafeArea>
  );
};

export default EditEducationListScreen;
