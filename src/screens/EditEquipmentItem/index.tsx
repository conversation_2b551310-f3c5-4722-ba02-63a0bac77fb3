import { SafeAreaView, ScrollView } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditEquipmentItem from './components/EditEquipmentItem';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditEquipmentItem'>;

const EditEquipmentItemScreen = () => {
  const route = useRoute<RouteProps>();
  const { profileId, experienceId, shipId, equipmentId, data, refetch } = route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeAreaView className="flex-1 bg-white">
      <EditEquipmentItem
        onBack={navigation.goBack}
        profileId={profileId}
        experienceId={experienceId}
        shipId={shipId!}
        equipmentId={equipmentId!}
        preFilledData={data}
        refetch={refetch}
      />
    </SafeAreaView>
  );
};

export default EditEquipmentItemScreen;
