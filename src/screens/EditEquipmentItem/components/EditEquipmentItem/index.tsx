import { useEffect, useState } from 'react';
import { Pressable, SafeAreaView, ScrollView, Text, View } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import ChipInput from '@/src/components/ChipInput';
import EntitySearch from '@/src/components/EntitySearch';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { AppDispatch, RootState } from '@/src/redux/store';
import { EditEquipmentItemPropsI, EquipmentDetailsFormDataI } from './types';
import { useEditEquipmentItem } from './useHook';

const EditEquipmentItem = ({
  onBack,
  profileId,
  experienceId,
  shipId,
  equipmentId,
  preFilledData,
  refetch,
}: EditEquipmentItemPropsI) => {
  const {
    methods,
    onSubmit,
    isSubmitting,
    localFuelTypes,
    setLocalFuelTypes,
    clearFields,
    isSubmitted,
    setIsSubmitted,
  } = useEditEquipmentItem(
    profileId,
    experienceId,
    shipId,
    equipmentId,
    onBack,
    preFilledData,
    refetch,
  );
  const { control, handleSubmit } = methods;

  const categorySelection = useSelector(selectSelectionByKey('category')) as SearchResultI & {
    hasFuelType: boolean;
  };

  useEffect(() => {
    if (categorySelection) {
      methods.setValue('category', categorySelection);
    }

    return () => {
      clearFields();
    };
  }, [categorySelection]);

  const renderField = (
    name: keyof Pick<
      EquipmentDetailsFormDataI,
      'manufacturerName' | 'model' | 'power' | 'additionalDetails'
    >,
    label: string,
    isRequired?: boolean,
  ) => (
    <Controller
      control={control}
      name={name}
      rules={isRequired && isSubmitted ? { required: `${label} is required` } : {}}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <TextInput
          label={label}
          value={value}
          onChangeText={onChange}
          placeholder={`Enter ${label.toLowerCase()}`}
          error={error?.message}
          className="py-3"
          key={label}
        />
      )}
    />
  );

  return (
    <ScrollView className="flex-1 bg-white px-4" showsVerticalScrollIndicator={false}>
      <View className="flex-row items-center justify-between py-4">
        <BackButton onBack={onBack} label="Edit Equipment" />
        <Pressable
          onPress={() => {
            setIsSubmitted(true);
            handleSubmit(onSubmit)();
          }}
          disabled={isSubmitting}
        >
          <Text className="text-lg font-medium text-[#448600]">
            {isSubmitting ? 'Submitting' : 'Save'}
          </Text>
        </Pressable>
      </View>
      <EntitySearch
        title="Category"
        placeholder="Enter Category"
        selectionKey="category"
        data={categorySelection ? categorySelection?.name : methods.watch('category')?.name}
        error={
          isSubmitted && !categorySelection && !methods.watch('category')?.id
            ? `Category is required`
            : undefined
        }
      />
      {renderField('manufacturerName', 'Manufacturer Name', true)}
      {renderField('model', 'Model', true)}
      {renderField('power', 'Power/Capacity', false)}
      {(categorySelection?.hasFuelType || methods.watch('category').hasFuelType) && (
        <>
          <ChipInput
            title="Fuel Type"
            chips={localFuelTypes}
            onRemove={(id) => setLocalFuelTypes((prev) => prev.filter((s) => s.id !== id))}
            disabled={localFuelTypes.length >= 10}
          />
          {localFuelTypes.length >= 10 && (
            <Text className="text-red-500 text-xs mt-1">
              You can only select up to 10 fuel types. Please remove some before adding others.
            </Text>
          )}
        </>
      )}

      {renderField('additionalDetails', 'Additional Details(Optional)', false)}
    </ScrollView>
  );
};

export default EditEquipmentItem;
