import type React from 'react';
import { useState, useRef } from 'react';
import { View, TextInput, Pressable, Platform, ActivityIndicator } from 'react-native';
import Send from '@/src/assets/svgs/Send';
import type { MessageInputProps } from './types';
import { useMessageInput } from './useMessageInput';

export const MessageInput: React.FC<MessageInputProps> = ({
  value,
  onChangeText,
  onSend,
  loading,
}) => {
  const { handleKeyPress } = useMessageInput(onSend);
  const [inputHeight, setInputHeight] = useState(20);
  const textInputRef = useRef<TextInput>(null);

  const handleContentSizeChange = (event: any) => {
    const newHeight = Math.min(Math.max(20, event.nativeEvent.contentSize.height), 100);
    setInputHeight(newHeight);
  };

  return (
    <View className="border-t border-gray-200 bg-white py-3">
      <View className="flex-row items-end px-4 gap-3">
        <View
          className="flex-1 rounded-full px-4 py-3 border"
          style={{
            backgroundColor: '#F3ECEC',
            borderColor: '#DEDEDE',
            minHeight: Math.max(44, inputHeight + 24),
          }}
        >
          <TextInput
            ref={textInputRef}
            value={value}
            onChangeText={onChangeText}
            onContentSizeChange={handleContentSizeChange}
            placeholder="Type a message..."
            placeholderTextColor="#9CA3AF"
            className="text-sm text-gray-900"
            multiline
            textAlignVertical="center"
            style={{
              maxHeight: 100,
              minHeight: inputHeight,
            }}
            editable={!loading}
            scrollEnabled={inputHeight >= 100}
            onKeyPress={handleKeyPress}
            returnKeyType="send"
            blurOnSubmit={Platform.OS === 'android'}
          />
        </View>

        <Pressable
          className={`w-11 h-11 rounded-full items-center justify-center ${
            value.trim() ? 'bg-green-800' : 'bg-gray-400'
          } ${loading ? 'opacity-60' : ''}`}
          onPress={onSend}
          disabled={loading || !value.trim()}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Send color="white" width={2} height={2} />
          )}
        </Pressable>
      </View>
    </View>
  );
};
