import { Pressable, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import EntitySearch from '@/src/components/EntitySearch';
import { StatusLabel } from '@/src/components/StatusLabel';
import { StatusVariantI } from '@/src/components/StatusLabel/types';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { capitalizeFirstLetter } from '@/src/utilities/data/string';
import { STATUS_LABEL_MAP } from './const';
import { PropsI } from './types';
import { useEditShipDetails } from './useHook';

const EditShipDetailForm = (props: PropsI) => {
  const { type, handleBack, action, text } = props;
  const { methods, onSubmit, categories, loading } = useEditShipDetails(type, text);

  const flagSelection = useSelector(selectSelectionByKey('country')) as SearchResultI;
  const subVesselTypeSelection = useSelector(
    selectSelectionByKey('subVesselType'),
  ) as SearchResultI;
  const { handleSubmit, control } = methods;
  const isAddNewShip = action === 'add';

  return (
    <View className="p-3 bg-white flex-1">
      <View className="flex-row items-center justify-between mt-12">
        <BackButton
          onBack={handleBack}
          label={`${capitalizeFirstLetter(type)} details`}
          labelClassname="text-lg leading-6 font-medium"
        />
        <Pressable onPress={handleSubmit(onSubmit)} disabled={loading}>
          <Text
            className={`text-lg font-medium ${loading ? 'text-gray-400' : 'text-primaryGreen'}`}
          >
            {loading ? 'Submitting...' : 'Submit'}
          </Text>
        </Pressable>
      </View>
      <View className="mt-5 flex flex-col gap-4">
        {categories.map((category) => {
          const showStatus = !isAddNewShip && !!category.status;

          if (category.key === 'flag') {
            return (
              <View key={category.id} className="flex gap-1">
                <Controller
                  control={control}
                  name={category.key}
                  render={({ fieldState: { error } }) => (
                    <EntitySearch
                      title={category.title}
                      placeholder={`Search ${category.title.toLowerCase()}`}
                      selectionKey="country"
                      data={flagSelection ? flagSelection.name : methods.watch('flag') || ''}
                    />
                  )}
                />
                {showStatus && category.status ? (
                  <StatusLabel
                    label={STATUS_LABEL_MAP[category.status as keyof typeof STATUS_LABEL_MAP]}
                    variant={category.status as StatusVariantI}
                  />
                ) : null}
              </View>
            );
          }

          if (category.key === 'subVesselType') {
            return (
              <View key={category.id} className="flex gap-1">
                <Controller
                  control={control}
                  name={category.key}
                  render={({ fieldState: { error } }) => (
                    <EntitySearch
                      title={category.title}
                      placeholder={`Search ${category.title.toLowerCase()}`}
                      selectionKey="subVesselType"
                      data={
                        subVesselTypeSelection
                          ? subVesselTypeSelection.name
                          : methods.watch('subVesselType') || ''
                      }
                    />
                  )}
                />
                {showStatus && category.status ? (
                  <StatusLabel
                    label={STATUS_LABEL_MAP[category.status as keyof typeof STATUS_LABEL_MAP]}
                    variant={category.status as StatusVariantI}
                  />
                ) : null}
              </View>
            );
          }

          return (
            <View key={category.id} className="flex gap-1">
              <Controller
                control={control}
                name={category.key}
                rules={{ required: 'Details is required' }}
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <TextInput
                    label={category.title}
                    value={value}
                    onChangeText={onChange}
                    placeholder={`Enter ${category.title}`}
                    error={error?.message}
                  />
                )}
              />
              {showStatus && category.status ? (
                <StatusLabel
                  label={STATUS_LABEL_MAP[category.status as keyof typeof STATUS_LABEL_MAP]}
                  variant={category.status as StatusVariantI}
                />
              ) : null}
            </View>
          );
        })}
      </View>
    </View>
  );
};

export default EditShipDetailForm;
