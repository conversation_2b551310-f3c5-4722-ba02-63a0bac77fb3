/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ScrollView, View } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Button from '@/src/components/Button';
import { AppStackParamListI } from '@/src/navigation/types';
import EditShipDetailForm from './components/EditShipDetailForm';
import { DetailScreenTypeI } from './components/EditShipDetailForm/types';

const EditDetailScreen = () => {
  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();
  const route = useRoute<RouteProp<AppStackParamListI, 'EditDetail'>>();
  const { text, type, action } = route?.params;

  const isShip = type === 'ship';

  const displayAddDetails = !isShip && action !== 'add';

  const handleBack = () => {
    navigation.pop();
  };

  const handleDetails = () => {
    // navigation.navigate('AddDetail', { id, type });
  };

  const formComponents = {
    ship: (
      <EditShipDetailForm
        text={text as string}
        action={action}
        type={type as DetailScreenTypeI}
        handleBack={handleBack}
      />
    ),
    // port: <EditPortDetailForm />,
  };

  return (
    <>
      <ScrollView className="bg-white flex-1">
        {formComponents[type as keyof typeof formComponents]}
      </ScrollView>
      {displayAddDetails ? (
        <View className="absolute bottom-0 left-0 right-0 bg-white px-3 py-3 border-t border-gray-200">
          <Button label={`Add ${type} details`} onPress={handleDetails} />
        </View>
      ) : (
        <></>
      )}
    </>
  );
};

export default EditDetailScreen;
