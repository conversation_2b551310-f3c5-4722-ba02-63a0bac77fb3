import { View, Text, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';

const PrivacyPolicyScreen = () => {
  const navigation = useNavigation();

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <View className="px-4 py-2">
          <BackButton onBack={handleBack} label="" />
        </View>

        <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false}>
          <View className="pb-8">
            <Text className="text-3xl font-bold text-gray-900 mb-2">Privacy Policy</Text>
            <Text className="text-sm text-gray-500 mb-8">Last updated: June 2025</Text>

            <View className="gap-y-6">
              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">Introduction</Text>
                <Text className="text-base text-gray-700 leading-6">
                  At Navicater, we respect your privacy and are committed to protecting your
                  personal data. This privacy policy explains how we collect, use, and safeguard
                  your information when you use our maritime professional networking platform.
                </Text>
              </View>

              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  Information We Collect
                </Text>
                <View className="gap-y-3">
                  <View>
                    <Text className="text-base font-medium text-gray-800 mb-1">
                      Personal Information
                    </Text>
                    <Text className="text-base text-gray-700 leading-6">
                      • Name, email address, and contact details{'\n'}• Professional information
                      including rank, company, and experience{'\n'}• Profile photos and documents
                      you choose to upload
                    </Text>
                  </View>
                  <View>
                    <Text className="text-base font-medium text-gray-800 mb-1">Usage Data</Text>
                    <Text className="text-base text-gray-700 leading-6">
                      • How you interact with our platform{'\n'}• Device information and IP address
                      {'\n'}• App usage analytics and performance data
                    </Text>
                  </View>
                </View>
              </View>

              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  How We Use Your Information
                </Text>
                <Text className="text-base text-gray-700 leading-6 mb-3">
                  We use your information to:
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  • Provide and improve our services{'\n'}• Connect you with other maritime
                  professionals{'\n'}• Send you relevant notifications and updates{'\n'}• Ensure
                  platform security and prevent fraud{'\n'}• Comply with legal obligations
                </Text>
              </View>

              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">Data Sharing</Text>
                <Text className="text-base text-gray-700 leading-6">
                  We do not sell your personal data. We may share your information only:
                </Text>
                <Text className="text-base text-gray-700 leading-6 mt-3">
                  • With other users as part of your public profile{'\n'}• With service providers
                  who help us operate the platform{'\n'}• When required by law or to protect our
                  rights{'\n'}• With your explicit consent
                </Text>
              </View>

              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">Data Security</Text>
                <Text className="text-base text-gray-700 leading-6">
                  We implement industry-standard security measures to protect your data, including
                  encryption, secure servers, and regular security audits. However, no method of
                  transmission over the internet is 100% secure.
                </Text>
              </View>

              <View>
                <Text className="text-xl font-semibold text-gray-900 mb-3">
                  Changes to This Policy
                </Text>
                <Text className="text-base text-gray-700 leading-6">
                  We may update this privacy policy from time to time. We will notify you of any
                  significant changes through the app or via email.
                </Text>
              </View>

              <View className="bg-green-50 rounded-xl p-4">
                <Text className="text-xl font-semibold text-green-800 mb-3">Contact Us</Text>
                <Text className="text-base text-green-700 leading-6">
                  If you have any questions about this privacy policy or how we handle your data,
                  please contact us:
                </Text>
                <Text className="text-base text-green-700 leading-6 mt-3">
                  Email: <EMAIL>{'\n'}
                  Address: Navicater Solutions
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </SafeArea>
  );
};

export default PrivacyPolicyScreen;
