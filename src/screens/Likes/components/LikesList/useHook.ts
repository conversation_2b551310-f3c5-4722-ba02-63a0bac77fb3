import { useState, useEffect } from 'react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectReactionsByPostId,
  selectScrapbookReactionsByPostId,
} from '@/src/redux/selectors/content';
import {
  fetchReactionsForPost,
  fetchScrapbookReactionsForPost,
} from '@/src/redux/slices/content/contentSlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { HomeStackParamListI } from '@/src/navigation/types';
import { UseLikesListResult } from './types';

const PAGE_SIZE = 10;

const useLikesList = (postId: string): UseLikesListResult => {
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const route = useRoute<RouteProp<HomeStackParamListI, 'Likes'>>();

  const type = route.params.type;

  const dispatch = useDispatch<AppDispatch>();
  const reactionsData = useSelector((state) =>
    type === 'SCRAPBOOK_POST'
      ? selectScrapbookReactionsByPostId(state, postId)
      : selectReactionsByPostId(state, postId),
  );

  const fetchReactions = (postId: string, page: number, pageSize: number) => {
    return type === 'SCRAPBOOK_POST'
      ? dispatch(fetchScrapbookReactionsForPost({ postId, page, pageSize }))
      : dispatch(fetchReactionsForPost({ postId, page, pageSize }));
  };

  useEffect(() => {
    const loadInitialReactions = async () => {
      if (!postId) return;
      setLoading(true);
      try {
        const result = await fetchReactions(postId, 0, PAGE_SIZE).unwrap();
        const resultReactions = result.reactions || [];
        const resultTotal = result.totalCount || 0;
        setHasMore(resultReactions.length === PAGE_SIZE && resultReactions.length < resultTotal);
        setPage(0);
      } catch (error) {
        triggerErrorBoundary(
          new Error(
            `Failed to load reactions: ${error instanceof Error ? error.message : 'Unknown error'}`,
          ),
        );
      } finally {
        setLoading(false);
      }
    };

    loadInitialReactions();
  }, [postId, type]);

  const handleRefresh = async () => {
    if (refreshing || !postId) return;
    setRefreshing(true);
    try {
      const result = await fetchReactions(postId, 0, PAGE_SIZE).unwrap();
      const resultReactions = result.reactions || [];
      const resultTotal = result.totalCount || 0;
      setPage(0);
      setHasMore(resultReactions.length === PAGE_SIZE && resultReactions.length < resultTotal);
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to refresh reactions',
      });
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (loading || refreshing || !hasMore || !postId) return;
    const nextPage = page + 1;
    setLoading(true);
    try {
      const result = await fetchReactions(postId, nextPage, PAGE_SIZE).unwrap();
      const resultReactions = result.reactions || [];
      const resultTotal = result.totalCount || 0;
      setPage(nextPage);
      setHasMore(resultReactions.length === PAGE_SIZE && resultReactions.length < resultTotal);
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to load more reactions',
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    reactions: reactionsData,
    loading,
    refreshing,
    handleRefresh,
    handleLoadMore,
  };
};

export default useLikesList;
