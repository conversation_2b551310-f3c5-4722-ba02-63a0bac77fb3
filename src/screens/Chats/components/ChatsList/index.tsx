import { FlatList, View, ActivityIndicator, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { HomeStackParamListI } from '@/src/navigation/types';
import AiBot from '@/src/assets/images/others/aibot.png';
import ChatItem from '../ChatItem';
import TopBanner from '../TopBanner';
import { useChatsListHook } from './useHook';

const ChatsList = () => {
  const navigation = useNavigation<NativeStackNavigationProp<HomeStackParamListI>>();
  const {
    chats,
    loading,
    error,
    refreshing,
    loadingMore,
    handleRefresh,
    handleLoadMore,
    removeChat,
  } = useChatsListHook();

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#448600" />
      </View>
    );
  };

  if (loading && !refreshing && !loadingMore) {
    return (
      <View className="flex-1 bg-white items-center justify-center">
        <ActivityIndicator size="small" color="#448600" />
        <Text className="text-gray-500 mt-2">Loading chats...</Text>
      </View>
    );
  }

  if (error && !refreshing && !loadingMore) {
    return (
      <View className="flex-1 bg-white items-center justify-center">
        <Text className="text-red-500 text-center px-4">{error}</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white">
      <FlatList
        data={chats}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <ChatItem item={item} onDelete={removeChat} />}
        ItemSeparatorComponent={() => <View className="h-px bg-borderGrayLight" />}
        ListHeaderComponent={
          <View>
            <TopBanner
              title="Chat with Navicater AI"
              subtitle="Get all your questions answered and find relevant chats easily"
              iconSource={AiBot}
              onPress={() => navigation.navigate('AIChat')}
            />
          </View>
        }
        refreshing={refreshing}
        onRefresh={handleRefresh}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={
          <View className="flex-1 items-center justify-center py-20">
            <Text className="text-gray-500 text-center">No chats yet</Text>
            <Text className="text-gray-400 text-center mt-1">Start a conversation!</Text>
          </View>
        }
      />
    </View>
  );
};

export default ChatsList;
