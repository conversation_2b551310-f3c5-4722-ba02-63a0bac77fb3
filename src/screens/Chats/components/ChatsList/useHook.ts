import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { findAllIndividualChats } from '@/src/networks/chat/individual';
import { fetchProfileAPI } from '@/src/networks/profile/userProfile';
import { ChatListItem, ProfileData } from '../ChatItem/types';
import { ChatsListState } from './types';

export const useChatsListHook = () => {
  const [state, setState] = useState<ChatsListState>({
    chats: [],
    loading: true,
    error: null,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    currentPage: 0,
    totalChats: 0,
  });
  const currentUser = useSelector(selectCurrentUser);
  const PAGE_SIZE = 10;

  const fetchProfileData = async (profileId: string): Promise<ProfileData> => {
    try {
      const result = (await fetchProfileAPI(profileId)) as ProfileData;
      return result;
    } catch (error) {
      return {
        email: '',
        name: 'Unknown User',
        username: '',
        avatar: null,
        profileId: profileId,
        designation: null,
        entity: null,
      };
    }
  };

  const loadChats = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setState((prev) => ({ ...prev, refreshing: true, error: null }));
      } else if (state.loadingMore) {
        return;
      } else if (!isRefresh && !state.loading) {
        setState((prev) => ({ ...prev, loadingMore: true }));
      }

      const page = isRefresh ? 0 : state.currentPage + (state.loadingMore ? 1 : 0);

      const result = await findAllIndividualChats({
        page,
        pageSize: PAGE_SIZE,
      });

      const chatsWithProfiles = await Promise.all(
        result.data.map(async (chat) => {
          const profileId =
            chat.senderId === currentUser.profileId ? chat.recieverId : chat.senderId;
          const profile = await fetchProfileData(profileId);
          return {
            ...chat,
            profile,
          } as ChatListItem;
        }),
      );

      setState((prev) => ({
        ...prev,
        chats: isRefresh ? chatsWithProfiles : [...prev.chats, ...chatsWithProfiles],
        currentPage: page,
        totalChats: result.total,
        hasMore: result.hasMore,
        loading: false,
        refreshing: false,
        loadingMore: false,
        error: null,
      }));
    } catch (err) {
      setState((prev) => ({
        ...prev,
        error: 'Failed to load chats',
        loading: false,
        refreshing: false,
        loadingMore: false,
      }));
    }
  };

  const removeChat = (chatId: string) => {
    setState((prev) => ({
      ...prev,
      chats: prev.chats.filter((chat) => chat.id !== chatId),
      totalChats: prev.totalChats - 1,
    }));
  };

  const handleRefresh = () => {
    loadChats(true);
  };

  const handleLoadMore = () => {
    if (!state.loadingMore && state.hasMore) {
      loadChats(false);
    }
  };

  useEffect(() => {
    loadChats();
  }, []);

  return {
    ...state,
    handleRefresh,
    handleLoadMore,
    removeChat,
  };
};
