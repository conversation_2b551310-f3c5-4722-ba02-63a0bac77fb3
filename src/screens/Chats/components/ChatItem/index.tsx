import { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import CustomModal from '@/src/components/Modal';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { formatSocialTime } from '@/src/utilities/datetime';
import { BottomTabNavigationI } from '@/src/navigation/types';
import { deleteSpecificProfileChat } from '@/src/networks/chat/individual';
import { ChatItemProps } from './types';

const ChatItem = ({ item, onDelete }: ChatItemProps) => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const currentUser = useSelector(selectCurrentUser);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const onPress = () => {
    const currentUserId = currentUser.profileId;
    const profileId = item.senderId === currentUserId ? item.recieverId : item.senderId;

    navigation.navigate('Chat', {
      id: profileId,
    });
  };

  const onLongPress = () => {
    setIsModalVisible(true);
  };

  const handleDeleteChat = async () => {
    const currentUserId = currentUser.profileId;
    const profileId = item.senderId === currentUserId ? item.recieverId : item.senderId;

    setIsDeleting(true);
    try {
      await deleteSpecificProfileChat(profileId);
      setIsModalVisible(false);
      onDelete(item.id);
    } catch (error) {
      console.error('Error deleting chat:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const getLastMessageText = () => {
    if (item.content?.text) {
      return item.content.text;
    }
    if (item.content?.media && item.content.media.length > 0) {
      return `${item.content.media.length} media file(s)`;
    }
    return 'No message';
  };

  return (
    <>
      <Pressable onPress={onPress} onLongPress={onLongPress}>
        <View className="flex-row items-center justify-between p-3">
          <UserAvatar
            avatarUri={item.profile?.avatar!}
            width={50}
            height={50}
            name={item.profile?.name || 'Unknown'}
          />
          <View className="flex-1 px-3">
            <Text className="text-base font-semibold text-black">
              {item.profile?.name || 'Unknown User'}
            </Text>
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              className="text-sm text-gray-500 mt-0.5 w-full"
            >
              {getLastMessageText()}
            </Text>
          </View>
          <Text className="text-xs text-gray-400">
            {formatSocialTime(new Date(item.createdAt).getTime())}
          </Text>
        </View>
      </Pressable>

      <CustomModal
        isVisible={isModalVisible}
        title="Delete Chat"
        description="Are you sure you want to delete this chat? This action cannot be undone."
        cancelText="Cancel"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={handleDeleteChat}
        onCancel={() => setIsModalVisible(false)}
        isConfirming={isDeleting}
      />
    </>
  );
};

export default ChatItem;
