import { useState, useCallback, memo } from 'react';
import { FlatList, Text, View, ActivityIndicator, RefreshControl, Pressable } from 'react-native';
import NotificationItem from '@/src/components/NotificationItem';
import Tabs from '@/src/components/Tabs';
import type { NotificationFetchManyQueryI } from '@/src/networks/notifications/types';
import { useNotificationList } from './useHook';

const EmptyNotificationList = memo(({ activeTab }: { activeTab: string }) => (
  <View className="flex-1 items-center justify-center py-20">
    <View className="w-20 h-20 rounded-full bg-gray-100 items-center justify-center mb-4">
      <Text className="text-3xl">🔔</Text>
    </View>
    <Text className="text-lg font-medium text-gray-900 mb-2">
      {activeTab === 'unread' ? 'No unread notifications' : 'No notifications yet'}
    </Text>
    <Text className="text-sm text-gray-500 text-center px-8">
      {activeTab === 'unread'
        ? 'All caught up! No new notifications to show'
        : "When you get notifications, they'll show up here"}
    </Text>
  </View>
));

const LoadingFooter = memo(() => (
  <View className="py-4 items-center">
    <ActivityIndicator size="small" color="#4CAF50" />
  </View>
));

const InitialLoader = memo(() => (
  <View className="flex-1 items-center justify-center">
    <ActivityIndicator size="small" color="#4CAF50" />
    <Text className="text-gray-500 mt-4 text-base">Loading notifications...</Text>
  </View>
));

const MemoizedNotificationItem = memo(NotificationItem);

const NotificationList = () => {
  const [activeTab, setActiveTab] = useState('all');
  const {
    notifications,
    loading,
    initialLoading,
    hasMore,
    loadMore,
    refresh,
    refreshing,
    markAllAsRead,
    markAsRead,
    getProfileById,
    getPostById,
  } = useNotificationList();

  const unreadNotifications = notifications.filter((n) => !n.isRead);
  const displayNotifications = activeTab === 'unread' ? unreadNotifications : notifications;

  const tabs = [
    { id: 'all', label: 'All' },
    {
      id: 'unread',
      label: `Unread${unreadNotifications.length > 0 ? ` (${unreadNotifications.length})` : ''}`,
    },
  ];

  const renderItem = useCallback(
    ({ item }: { item: NotificationFetchManyQueryI }) => {
      try {
        return (
          <MemoizedNotificationItem
            notification={item}
            profile={getProfileById(item.data.actorProfileId)}
            post={item.data.postId ? getPostById(item.data.postId) : undefined}
            onMarkAsRead={markAsRead}
          />
        );
      } catch (error) {
        console.error('Error rendering notification item:', error);
        return null;
      }
    },
    [getProfileById, getPostById, markAsRead],
  );

  const keyExtractor = useCallback((item: NotificationFetchManyQueryI) => item.id, []);

  const renderFooter = () => {
    if (!hasMore || activeTab === 'unread') return null;
    return loading ? <LoadingFooter /> : null;
  };

  const handleMarkAllRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  if (initialLoading) {
    return <InitialLoader />;
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="bg-white border-b border-gray-100">
        <View className="flex-row items-center justify-between px-4 py-3">
          <View className="flex-1">
            <Tabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
          </View>
          {unreadNotifications.length > 0 && (
            <Pressable onPress={handleMarkAllRead} className="px-3 py-1.5 ml-4">
              <Text className="text-green-800 text-sm font-medium">Mark all read</Text>
            </Pressable>
          )}
        </View>
      </View>
      <FlatList
        data={displayNotifications}
        keyExtractor={keyExtractor}
        renderItem={renderItem}
        className="flex-1"
        showsVerticalScrollIndicator={false}
        contentContainerClassName="pb-4"
        ListEmptyComponent={<EmptyNotificationList activeTab={activeTab} />}
        ListFooterComponent={renderFooter}
        onEndReached={activeTab === 'all' ? loadMore : undefined}
        onEndReachedThreshold={0.5}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={refresh}
            colors={['#4CAF50']}
            tintColor="#4CAF50"
          />
        }
        removeClippedSubviews={false}
        initialNumToRender={5}
        maxToRenderPerBatch={5}
        windowSize={5}
      />
    </View>
  );
};

export default NotificationList;
