/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditCertificationList from './components/EditCertificationList';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditCertificationList'>;

const EditCertificationListScreen = () => {
  const route = useRoute<RouteProps>();
  const { editable, tab } = route.params;
  const { profileId } = useSelector(selectCurrentUser);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <EditCertificationList
        profileId={profileId!}
        onBack={navigation.goBack}
        editable={editable!}
        tab={tab!}
      />
    </SafeArea>
  );
};

export default EditCertificationListScreen;
