import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import { UseEditCertificationListI } from './types';

export const useEditCertificationList = (profileId: string): UseEditCertificationListI => {
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  const onAddCertification = () => {
    navigation.navigate('EditCertificationItem', { profileId });
  };

  return {
    onAddCertification,
    navigation,
  };
};
