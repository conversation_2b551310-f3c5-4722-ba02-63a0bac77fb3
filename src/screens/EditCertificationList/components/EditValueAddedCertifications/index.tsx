import React from 'react';
import { ActivityIndicator, FlatList, Pressable, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import { selectValueAddedCertifications } from '@/src/redux/selectors/about';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { CertificationI } from '../EditCertificationList/types';
import { useEditValueAdded } from './useHook';

export const EditValueAdded = ({
  profileId,
  editable,
}: {
  profileId: string;
  editable: boolean;
}) => {
  const certifications = useSelector(selectValueAddedCertifications);
  const {
    onEditCertification,
    onDeleteCertification,
    isVisible,
    setIsVisible,
    setDeleteCertificationId,
    isDeleting,
    loading,
  } = useEditValueAdded(profileId);

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  const renderItem = ({ item, index }: { item: CertificationI; index: number }) => {
    const isLast = index === certifications.length - 1;
    return (
      <View className={`py-4 ${isLast ? '' : 'border-b border-[#E5E5E5]'}`}>
        <Text className="text-base font-medium">{item.certificateCourse.name}</Text>
        <View className="flex-row justify-between mt-2 items-center">
          <Text className="text-sm text-[#737373] mt-1 max-w-[280]">{item.entity.name}</Text>
          {editable && (
            <View className="flex-row gap-1 items-center">
              <Pressable onPress={() => onEditCertification(item.id)} className="p-2">
                <EditPencil />
              </Pressable>
              <Pressable
                onPress={() => {
                  setDeleteCertificationId(item.id);
                  setIsVisible(true);
                }}
                className="p-2"
              >
                <DeleteIcon />
              </Pressable>
            </View>
          )}
        </View>
      </View>
    );
  };

  return certifications.length === 0 ? (
    <NotFound />
  ) : (
    <>
      <FlatList
        data={certifications}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
      />
      <CustomModal
        isVisible={isVisible}
        onCancel={() => setIsVisible(false)}
        title="Are you sure you want to delete this certification?"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={onDeleteCertification}
        isConfirming={isDeleting}
      />
    </>
  );
};
