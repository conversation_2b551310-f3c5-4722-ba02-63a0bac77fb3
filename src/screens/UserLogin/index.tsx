/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableWithoutFeedback,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import { AppStackParamListI } from '@/src/navigation/types';
import SignInForm from './components/SigninForm';

type SignInScreenNavigationProp = StackNavigationProp<AppStackParamListI>;

const SignInScreen = () => {
  const navigation = useNavigation<SignInScreenNavigationProp>();

  const handleForgotPassword = () => {};

  const handleSignUp = () => {
    navigation.navigate('CreateAccount');
  };

  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ flex: 1 }}
        >
          <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="handled">
            <SignInForm onForgotPassword={handleForgotPassword} onSignUp={handleSignUp} />
          </ScrollView>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default SignInScreen;
