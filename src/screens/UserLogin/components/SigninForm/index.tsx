/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { Image } from 'react-native';
import { Controller } from 'react-hook-form';
import Button from '@/src/components/Button';
import TextInput from '@/src/components/TextInput';
import TextView from '@/src/components/TextView';
import { emailRegex } from '@/src/consts/regEx';
import { SignInPropsI } from './types';
import useSignIn from './useHook';

const SignInForm = ({ onForgotPassword, onSignUp }: SignInPropsI) => {
  const { methods, isSubmitting, isGoogleSubmitting, onSubmit, handleGoogleLogin } = useSignIn();
  const {
    control,
    handleSubmit,
    formState: { isValid, errors },
  } = methods;

  return (
    <View className="px-5">
      <View className="my-8">
        <TextView title="Sign in" />
      </View>
      <View>
        <Controller
          control={control}
          name="email"
          rules={{
            required: 'Email is required',
            pattern: {
              value: emailRegex,
              message: 'Invalid email address',
            },
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="Email ID"
              value={value}
              onChangeText={onChange}
              placeholder="Enter email ID"
              error={errors.email?.message}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          )}
        />
      </View>
      <View className="mt-6">
        <Controller
          control={control}
          name="password"
          rules={{ required: 'Password is required' }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="Password"
              value={value}
              placeholder="Enter password"
              type="password"
              onChangeText={onChange}
              error={errors.password?.message}
            />
          )}
        />
        <Pressable onPress={onForgotPassword} className="mt-3">
          <Text className="text-sm text-[#448600] font-medium leading-4 ">Forgot password</Text>
        </Pressable>
      </View>
      <View className="mt-8 space-y-4">
        <Button
          label="Sign in"
          onPress={handleSubmit(onSubmit)}
          variant={isValid ? 'primary' : 'tertiary'}
          disabled={!isValid || isSubmitting}
          loading={isSubmitting}
          labelClassName=" font-medium leading-4 text-base"
        />
        <Button
          label="Sign in with Google"
          onPress={handleGoogleLogin}
          variant={'secondary'}
          disabled={isGoogleSubmitting}
          loading={isGoogleSubmitting}
          labelClassName="text-[#525252] font-medium leading-4 text-base"
          className="mt-5"
          prefixIcon={
            <Image
              source={require('@/src/assets/images/oauth/google.png')}
              className="w-6 h-6"
              resizeMode="contain"
            />
          }
        />
        <View className="flex-row justify-center items-center mt-8">
          <Text className="text-sm text-[#333333]">Don't have an account? </Text>
          <Pressable onPress={onSignUp}>
            <Text className="text-sm text-[#448600] font-medium font-inter-medium">Sign up</Text>
          </Pressable>
        </View>
      </View>
    </View>
  );
};

export default SignInForm;
