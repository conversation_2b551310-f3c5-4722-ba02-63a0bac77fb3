/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { UseFormReturn } from 'react-hook-form';
import { VoidFnI } from '@/src/types/common/function';

export interface SignInFormDataI {
  email: string;
  password: string;
}

export interface UseSignInFormI {
  methods: UseFormReturn<SignInFormDataI>;
  isSubmitting: boolean;
  isGoogleSubmitting: boolean;
  onSubmit: (data: SignInFormDataI) => Promise<void>;
  handleGoogleLogin: VoidFnI;
}

export interface SignInPropsI {
  onForgotPassword: () => void;
  onSignUp: () => void;
}
