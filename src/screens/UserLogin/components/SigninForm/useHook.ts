/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import Config from 'react-native-config';
import {
  GoogleSignin,
  isSuccessResponse,
  type SignInResponse,
} from '@react-native-google-signin/google-signin';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { fetchAndSaveUserProfile } from '@/src/redux/slices/user/userSlice';
import { googleSignInAsync, signInAsync } from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import AppError from '@/src/errors/networks/AppError';
import { BottomTabNavigationI } from '@/src/navigation/types';
import useNotification from '@/src/hooks/notification';
import useStorage from '@/src/hooks/storage';
import { AuthLoginResultI } from '@/src/networks/auth/types';
import { SignInFormDataI, UseSignInFormI } from './types';

GoogleSignin.configure({
  scopes: ['openid', 'profile', 'email'],
  webClientId: Config.WEB_CLIENT_ID,
  iosClientId: Config.IOS_CLIENT_ID,
  profileImageSize: 120,
  offlineAccess: true,
});

const useSignIn = (): UseSignInFormI => {
  const { setStorage } = useStorage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGoogleSubmitting, setIsGoogleSubmitting] = useState(false);
  const navigation = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch<AppDispatch>();
  const { getDeviceToken } = useNotification();
  const defaultValues = { email: '', password: '' };

  const methods = useForm<SignInFormDataI>({
    mode: 'onChange',
    defaultValues,
  });

  const navigateBasedOnUserState = (authResult: AuthLoginResultI) => {
    const { isUsernameSaved, isPersonalDetailsSaved, isWorkDetailsSaved, email } = authResult;

    if (!isUsernameSaved) {
      navigation.navigate('SetUsername', { email: email });
    } else if (!isPersonalDetailsSaved || !isWorkDetailsSaved) {
      navigation.navigate('AddUserDetailScreen');
    }
  };

  const onSubmit = async (data: SignInFormDataI) => {
    try {
      setIsSubmitting(true);
      const { email, password } = data;
      const deviceToken = await getDeviceToken();
      if (!deviceToken) {
        throw new AppError('Allow permission for notifications');
      }
      const result = await dispatch(signInAsync({ email, password, deviceToken })).unwrap();
      console.log(result.service_jwt_token);

      if (result.token) {
        await setStorage('token', result.token);
        await setStorage('service_jwt_token', result.service_jwt_token);
        await dispatch(fetchAndSaveUserProfile({ id: result.profileId })).unwrap();
        navigateBasedOnUserState(result);
      }
    } catch (err: unknown) {
      showToast({
        type: 'error',
        message: 'Sign In Failed',
        description: 'Currently in pipeline',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleLogin = async (): Promise<void> => {
    try {
      setIsGoogleSubmitting(true);
      await GoogleSignin.hasPlayServices();
      const currentGoogleUser = GoogleSignin.getCurrentUser();
      if (currentGoogleUser) await GoogleSignin.signOut();
      const response: SignInResponse = await GoogleSignin.signIn();
      if (isSuccessResponse(response) && response.data?.idToken) {
        const deviceToken = await getDeviceToken();
        if (!deviceToken) {
          throw new AppError('Allow permission for notifications');
        }
        const result = await dispatch(
          googleSignInAsync({
            googleToken: response.data.idToken,
            deviceToken,
          }),
        ).unwrap();

        if (result.token) {
          await setStorage('token', result.token);
          await setStorage('service_jwt_token', result.service_jwt_token);
          await dispatch(fetchAndSaveUserProfile({ id: result.profileId })).unwrap();
          navigateBasedOnUserState(result);
        }
      } else {
        throw new AppError('Google sign-in failed');
      }
    } catch (_error) {
      console.log({ _error });

      handleError(_error, {
        handle4xxError: () => {
          showToast({
            message: 'Google Sign In Failed',
            description: 'Unable to sign in with Google',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsGoogleSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    isGoogleSubmitting,
    handleGoogleLogin,
  };
};

export default useSignIn;
