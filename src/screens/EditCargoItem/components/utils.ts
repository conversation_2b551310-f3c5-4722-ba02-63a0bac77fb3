import { ShipCreateEditPayloadI } from '../../EditShipItem/components/EditShipItem/types';
import { toMonthYear } from '../../EditShipItem/components/utils';
import { CargoCreateEditPayloadI, CargoDetailsFormDataI } from './EditCargoItem/types';

export const generatePayloadCargo = (
  data: CargoDetailsFormDataI,
  preFilledData: ShipCreateEditPayloadI[],
  cargoId?: string,
) => {
  const cargo: CargoCreateEditPayloadI = {
    opr: cargoId ? 'UPDATE' : 'CREATE',
    name: data.name,
    description: data.description,
    fromDate: data.fromDate,
    toDate: data.toDate,
  };

  if (cargoId) {
    cargo['id'] = cargoId;
  }

  let payload = JSON.parse(JSON.stringify(preFilledData));
  payload[0].designations[0].ships[0]['cargos'] = [cargo];

  return payload;
};

export const validateToDate = (
  toDate: string | null,
  isPresent: boolean,
  fromDate: string,
  fieldFromDate: string,
  fieldToDate: string | null,
) => {
  if (!isPresent && !toDate) return 'toDate cannot be null';

  if (!toDate) return true;

  if (new Date(toDate) < new Date(fromDate)) {
    return 'To date cannot be before From date';
  }

  if (fieldToDate && new Date(toDate) > new Date(fieldToDate)) {
    return `Date cannot be after ${toMonthYear(fieldToDate)}`;
  }

  if (new Date(toDate) < new Date(fieldFromDate)) {
    return `Date cannot be before ${toMonthYear(fieldFromDate)}`;
  }

  return true;
};

export const validateFromDate = (
  fromDate: string,
  toDate: string | null,
  fieldFromDate: string,
  fieldToDate: string | null,
) => {
  if (new Date(fromDate) < new Date(fieldFromDate)) {
    return `Date cannot be before ${toMonthYear(fieldFromDate)}`;
  }

  if (toDate && new Date(fromDate) > new Date(toDate)) {
    return 'From date cannot be after To date';
  }

  if (fieldToDate && new Date(fromDate) > new Date(fieldToDate)) {
    return `Date cannot be after ${toMonthYear(fieldToDate)}`;
  }

  return true;
};
