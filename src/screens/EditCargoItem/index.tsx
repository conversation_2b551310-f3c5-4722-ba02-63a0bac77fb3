import { SafeAreaView } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditCargoItem from './components/EditCargoItem';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditCargoItem'>;

const EditCargoItemScreen = () => {
  const route = useRoute<RouteProps>();
  const { profileId, experienceId, shipId, cargoId, data, refetch, shipData } = route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeAreaView className="flex-1 bg-white">
      <EditCargoItem
        onBack={navigation.goBack}
        profileId={profileId}
        experienceId={experienceId}
        shipId={shipId!}
        cargoId={cargoId!}
        preFilledData={data}
        refetch={refetch}
        shipData={shipData}
      />
    </SafeAreaView>
  );
};

export default EditCargoItemScreen;
