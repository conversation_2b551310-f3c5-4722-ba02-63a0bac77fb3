import type React from 'react';
import { useEffect } from 'react';
import { ActivityIndicator, Text, View, Pressable, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { FlatList, RefreshControl } from 'react-native-gesture-handler';
import { useSelector, useDispatch } from 'react-redux';
import NotFound from '@/src/components/NotFound';
import UserAvatar from '@/src/components/UserAvatar';
import UserPost from '@/src/components/UserPost';
import { selectSearchPostsByTerm } from '@/src/redux/selectors/content';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  addReactionOptimistc,
  deletePostOptimistic,
  removeReactionOptimistic,
  revertDeletePostOptimistic,
  setSearchPosts,
} from '@/src/redux/slices/content/contentSlice';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import Company from '@/src/assets/svgs/Company';
import Institute from '@/src/assets/svgs/Institute';
import Ship from '@/src/assets/svgs/Ship';
import { deletePostAPI } from '@/src/networks/content/post';
import { deleteReactionAPI, upsertReactionAPI } from '@/src/networks/content/reaction';
import type { PostExternalClientI } from '@/src/networks/content/types';
import type { SearchResultItemI, SearchResultsProps } from './types';

const SearchResults: React.FC<SearchResultsProps> = ({
  activeTab,
  loading,
  searchResults,
  onLoadMore,
  onRefresh,
  refreshing,
  searchText,
  onError,
}) => {
  const currentUser = useSelector(selectCurrentUser);
  const searchPosts = useSelector((state) => selectSearchPostsByTerm(state, searchText || ''));
  const navigation = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch();

  const getConnectionDegree = (priority: number) => {
    switch (priority) {
      case 1:
        return '1st';
      case 2:
        return '2nd';
      case 3:
        return '3rd';
      default:
        return '';
    }
  };

  const handleLikePost = async (
    postId: string,
    isCurrentlyLiked: boolean,
    reactionType = 'LIKE',
  ) => {
    if (!currentUser) return;

    try {
      if (isCurrentlyLiked) {
        dispatch(removeReactionOptimistic({ postId }));
        await deleteReactionAPI({ postId });
      } else {
        dispatch(addReactionOptimistc({ postId }));
        await upsertReactionAPI({ postId, reactionType });
      }
    } catch (error) {
      if (isCurrentlyLiked) {
        dispatch(addReactionOptimistc({ postId }));
      } else {
        dispatch(removeReactionOptimistic({ postId }));
      }
      const errorMessage = `Failed to handle like post: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handleDeletePost = async (post: PostExternalClientI) => {
    try {
      dispatch(deletePostOptimistic({ post }));
      await deletePostAPI(post.id);
    } catch (error) {
      dispatch(revertDeletePostOptimistic({ postId: post.id }));
      const errorMessage = `Failed to delete post: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handlePostComment = (post: PostExternalClientI) => {
    navigation.navigate('HomeStack', {
      screen: 'Comment',
      params: {
        postId: post.id,
        type: 'USER_POST',
      },
    });
  };

  const handlePostEdit = (post: PostExternalClientI) => {
    navigation.navigate('CreateStack', {
      screen: 'CreateContent',
      params: {
        editing: true,
        postId: post.id,
        type: 'USER_POST',
      },
    });
  };

  const handleLikeCountPress = (postId: string) => {
    navigation.navigate('HomeStack', {
      screen: 'Likes',
      params: { postId: postId, type: 'USER_POST' },
    });
  };

  const renderPeopleItem = (item: SearchResultItemI) => (
    <Pressable
      onPress={() =>
        navigation.navigate('HomeStack', {
          screen: 'OtherUserProfile',
          params: {
            profileId: item.profileId,
            fromTabPress: false,
          },
        })
      }
      className="flex-row gap-4 items-center p-4 border-b border-gray-200"
    >
      <UserAvatar avatarUri={item.avatar || null} name={item.name} />
      <View className="flex-1">
        <View className="flex-row items-center justify-between">
          <Text
            className="text-lg font-medium text-gray-800"
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {item.name}
          </Text>
          {currentUser.profileId !== item.profileId && item.priority && (
            <View className="px-2 py-1 rounded-full bg-gray-100 ml-2">
              <Text className="text-xs font-medium text-gray-600">
                {getConnectionDegree(item.priority)}
              </Text>
            </View>
          )}
        </View>
        {item.designation && (
          <Text className="text-gray-500 mt-1 font-light" numberOfLines={1} ellipsizeMode="tail">
            {item.designation.name}
            {item.entity ? ' at ' + item.entity.name : ''}
          </Text>
        )}
      </View>
    </Pressable>
  );

  const renderInstitutionItem = (item: SearchResultItemI) => (
    <View className="flex-row gap-4 items-center p-4 border-b border-gray-200">
      <View className="w-10 h-10 rounded-full bg-green-50 items-center justify-center">
        <Institute />
      </View>
      <View className="flex-1">
        <Text className="text-lg font-medium text-gray-800" numberOfLines={1} ellipsizeMode="tail">
          {item.name}
        </Text>
        <Text className="text-gray-500 mt-1 font-light" numberOfLines={1} ellipsizeMode="tail">
          Institute
        </Text>
      </View>
    </View>
  );

  const renderOrganizationItem = (item: SearchResultItemI) => (
    <View className="flex-row gap-4 items-center p-4 border-b border-gray-200">
      <View className="w-10 h-10 rounded-full bg-green-50 items-center justify-center">
        <Company />
      </View>
      <View className="flex-1">
        <Text className="text-lg font-medium text-gray-800" numberOfLines={1} ellipsizeMode="tail">
          {item.name}
        </Text>
        <Text className="text-gray-500 mt-1 font-light" numberOfLines={1} ellipsizeMode="tail">
          Organization
        </Text>
      </View>
    </View>
  );

  const renderShipItem = (item: SearchResultItemI) => (
    <Pressable
      onPress={() => {
        navigation.navigate('HomeStack', {
          screen: 'ShipProfile',
          params: {
            dataType: item.dataType || '',
            imo: item.imo || '',
          },
        });
      }}
      className="flex-row gap-4 items-center p-4 border-b border-gray-200"
    >
      <View className="w-10 h-10 rounded-full bg-blue-50 items-center justify-center overflow-hidden">
        {item.imageUrl ? (
          <Image
            source={{ uri: item.imageUrl }}
            style={{ width: 40, height: 40, borderRadius: 20 }}
            resizeMode="cover"
          />
        ) : (
          <Ship />
        )}
      </View>
      <View className="flex-1">
        <Text className="text-lg font-medium text-gray-800" numberOfLines={1} ellipsizeMode="tail">
          {item.matchedName}
        </Text>
        <View>
          <Text className="text-gray-500 mt-1 font-light" numberOfLines={1} ellipsizeMode="tail">
            IMO: {item.imo}
          </Text>
        </View>
      </View>
    </Pressable>
  );

  const renderPortItem = (item: SearchResultItemI) => (
    <Pressable
      onPress={() => {
        navigation.navigate('HomeStack', {
          screen: 'PortProfile',
          params: {
            dataType: item.dataType || '',
            unLocode: item.unLocode || '',
          },
        });
      }}
      className="flex-row items-center gap-4 p-4 border-b border-gray-200"
    >
      <View className="w-12 h-12 rounded-full bg-amber-50 items-center justify-center overflow-hidden">
        {item.imageUrl ? (
          <Image
            source={{ uri: item.imageUrl }}
            style={{ width: 48, height: 48, borderRadius: 24 }}
            resizeMode="cover"
          />
        ) : (
          <Ship />
        )}
      </View>

      <View className="flex-1 justify-center">
        <Text
          className="text-base font-medium text-gray-800"
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {item.name}
        </Text>

        <View className="flex-row items-center justify-between mt-0.5">
          <Text className="text-sm text-gray-500" numberOfLines={1} ellipsizeMode="tail">
            {item.city?.name}
            {item.country ? `, ${item.country.name}` : ''}
          </Text>
          <Text className="text-sm text-gray-500 ml-2">{item.unLocode}</Text>
        </View>
      </View>
    </Pressable>
  );

  const renderPostItem = (item: PostExternalClientI) => {
    if (!item.Profile) return null;
    const isOwnPost = currentUser?.profileId === item.Profile.id;

    return (
      <UserPost
        post={item}
        onLikeCountPress={() => handleLikeCountPress(item.id)}
        onCommentPress={() => handlePostComment(item)}
        onDeletePress={() => handleDeletePost(item)}
        onEditPress={() => handlePostEdit(item)}
        onLikePress={() => handleLikePost(item.id, item.isLiked)}
        type="USER_POST"
        isOwnPost={isOwnPost}
      />
    );
  };

  const renderItem = ({ item }: { item: unknown }) => {
    switch (activeTab) {
      case 'people':
        return renderPeopleItem(item as SearchResultItemI);
      case 'organization':
        return renderOrganizationItem(item as SearchResultItemI);
      case 'ship':
        return renderShipItem(item as SearchResultItemI);
      case 'institution':
        return renderInstitutionItem(item as SearchResultItemI);
      case 'post':
        return renderPostItem(item as PostExternalClientI);
      case 'port':
        return renderPortItem(item as SearchResultItemI);
      default:
        return null;
    }
  };

  const renderFooter = () => {
    if (!loading) return null;

    return (
      <View className="py-4 flex items-center justify-center">
        <ActivityIndicator size="small" />
      </View>
    );
  };

  const getCurrentData = () => {
    if (activeTab === 'post') {
      return searchPosts.length > 0 ? searchPosts : searchResults?.data || [];
    }
    return searchResults?.data || [];
  };

  const currentData = getCurrentData();

  useEffect(() => {
    if (activeTab === 'post' && searchResults?.data && searchText) {
      const postsData = searchResults.data as PostExternalClientI[];
      if (postsData.length > 0) {
        dispatch(
          setSearchPosts({
            searchTerm: searchText,
            posts: postsData.map((post) => ({
              ...post,
              reactionsCount: post.reactionsCount || 0,
              totalCommentsCount: post.totalCommentsCount || 0,
              isLiked: post.isLiked || false,
            })),
          }),
        );
      }
    }
  }, [searchResults?.data, activeTab, searchText, dispatch]);

  return (
    <View className="flex-1">
      {loading && currentData.length === 0 ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="small" />
        </View>
      ) : currentData.length > 0 ? (
        <View className="flex-1">
          <FlatList
            data={currentData}
            extraData={searchPosts}
            keyExtractor={(item, index) =>
              `${activeTab}-${(item as SearchResultItemI).profileId || (item as SearchResultItemI).id || index}`
            }
            renderItem={renderItem}
            onEndReached={onLoadMore}
            onEndReachedThreshold={0.8}
            refreshControl={
              <RefreshControl enabled={true} refreshing={refreshing} onRefresh={onRefresh} />
            }
            ListFooterComponent={renderFooter}
            contentContainerStyle={{ flexGrow: 1 }}
            removeClippedSubviews={false}
            maxToRenderPerBatch={10}
            windowSize={10}
          />
        </View>
      ) : (
        <View className="flex-1 justify-center">
          <NotFound title="No Results Found" subtitle="Try searching for a different keyword" />
        </View>
      )}
    </View>
  );
};

export default SearchResults;
