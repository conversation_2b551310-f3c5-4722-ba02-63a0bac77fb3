import type { GlobalSearchCategory } from '@/src/utilities/search/types';

export interface RecentSearchesProps {
  setSearchData: (text: string) => void;
  setShowRecent: (flag: boolean) => void;
  setLoading: (flag: boolean) => void;
  setActiveTab: (tab: GlobalSearchCategory) => void;
  setLastSearchQuery: (query: string) => void;
  debouncedSearch: (...args: unknown[]) => Promise<Promise<unknown>>;
}

export interface RecentSearchItem {
  category: string;
  searchText: string;
}
