import React, { useState, useEffect } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import Tabs from '@/src/components/Tabs';
import type { HomeStackParamListI } from '@/src/navigation/types';
import { fetchShipProfile } from '@/src/networks/ship/profile';
import ShipAbout from './components/ShipAbout';
import type { ShipProfileData } from './components/ShipAbout/types';
import ShipHeader from './components/ShipHeader';
import ShipPeople from './components/ShipPeople';

const ShipScreen: React.FC = () => {
  const routes = useRoute<RouteProp<HomeStackParamListI, 'ShipProfile'>>();
  const shipId = routes.params?.imo;
  const shipDataType = routes.params?.dataType;

  const [activeTab, setActiveTab] = useState('about');
  const [loading, setLoading] = useState(true);
  const [shipData, setShipData] = useState<ShipProfileData | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const tabs = [
    { id: 'about', label: 'About' },
    { id: 'people', label: 'People' },
  ];

  useEffect(() => {
    if (!shipId) return;

    setLoading(true);
    fetchShipProfile({
      imo: shipId,
      dataType: shipDataType,
    })
      .then((data) => {
        setShipData(data as ShipProfileData);
      })
      .catch((err) => {
        const errorMessage = `Failed to fetch ship profile: ${err instanceof Error ? err.message : 'Unknown error'}`;
        triggerErrorBoundary(new Error(errorMessage));
      })
      .finally(() => {
        setLoading(false);
      });
  }, [shipId, shipDataType]);

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#5a8d3b" />
        </View>
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <View className="flex-1">
        <ShipHeader label={shipData?.name || 'Ship'} imageUri={shipData?.imageUrl || null} />

        <View className="my-4">
          <Tabs
            styles="bg-white border-b border-gray-100"
            activeTab={activeTab}
            onTabChange={setActiveTab}
            tabs={tabs}
          />
        </View>

        {activeTab === 'about' ? (
          <ShipAbout shipData={shipData} />
        ) : (
          <ShipPeople imo={shipId} dataType={shipDataType} />
        )}
      </View>
    </SafeArea>
  );
};

export default ShipScreen;
