import type React from 'react';
import { useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  Pressable,
  RefreshControl,
  Text,
  View,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import Button from '@/src/components/Button';
import { SectionHeader } from '@/src/components/SectionHeader';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import AddConnection from '@/src/assets/svgs/AddConnection';
import Education from '@/src/assets/svgs/Education';
import type { FetchPeopleResult } from '@/src/networks/ship/types';
import type { ShipPeopleProps } from './types';
import useShipPeople from './useHook';

const ShipPeople: React.FC<ShipPeopleProps> = ({ imo, dataType }) => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const currentUser = useSelector(selectCurrentUser);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const {
    people,
    loading,
    loadingMore,
    hasMore,
    refreshing,
    handleRefresh,
    handleLoadMore,
    connectionStates,
    handleConnectRequest,
  } = useShipPeople({
    imo,
    dataType,
    pageSize: 10,
    onError: triggerErrorBoundary,
  });

  const navigateToProfile = (profileId: string): void => {
    if (profileId === currentUser.profileId) {
      navigation.navigate('ProfileStack', {
        screen: 'UserProfile',
        params: { fromTabPress: false, profileId },
      });
    } else {
      navigation.navigate('HomeStack', {
        screen: 'OtherUserProfile',
        params: { profileId, fromTabPress: false },
      });
    }
  };

  const renderItem = ({ item }: { item: FetchPeopleResult }) => {
    const connectionState = connectionStates[item.id] || 'idle';

    return (
      <View
        className="bg-white rounded-lg p-6 mr-2 mt-3 items-center border border-gray-200"
        style={{ width: '48%' }}
      >
        <Image
          source={{
            uri: item.avatar || 'https://via.placeholder.com/100',
          }}
          className="w-28 h-28 border border-gray-100 rounded-full mb-2"
        />
        <Text className="font-medium text-base text-center" numberOfLines={2}>
          {item.name}
        </Text>
        <Text
          className="text-gray-500 text-sm mb-2 text-center"
          numberOfLines={2}
          ellipsizeMode="tail"
        >
          {item.designation?.name || ''}
        </Text>

        {item.isConnected === false ? (
          connectionState === 'success' ? (
            <View className="flex-row items-center bg-gray-100 py-1 px-4 rounded-lg">
              <Text className="text-gray-600">Request Sent</Text>
            </View>
          ) : (
            <Pressable
              className={`flex-row items-center ${
                connectionState === 'loading' ? 'bg-[#44860080]' : 'bg-[#448600]'
              } py-1 px-4 rounded-lg`}
              onPress={() => handleConnectRequest(item.id)}
              disabled={connectionState === 'loading'}
            >
              {connectionState === 'loading' ? (
                <ActivityIndicator color="white" />
              ) : (
                <>
                  <AddConnection color="white" />
                  <Text className="text-white">Connect</Text>
                </>
              )}
            </Pressable>
          )
        ) : (
          <Button
            variant="outline"
            label="View Profile"
            onPress={() => navigateToProfile(item.id)}
            className="rounded-md"
          />
        )}
      </View>
    );
  };

  const renderFooter = () =>
    loadingMore ? (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#166534" />
      </View>
    ) : null;

  const renderEmpty = () =>
    !loading ? (
      <View className="py-8 items-center">
        <Text className="text-gray-500 text-base">No visitors found</Text>
      </View>
    ) : null;

  if (loading) {
    return (
      <View className="bg-white flex-1 px-4 justify-center items-center">
        <ActivityIndicator size="small" color="#166534" />
        <Text className="mt-2 text-gray-600">Loading visitors...</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={people.data}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      numColumns={2}
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.3}
      contentContainerStyle={{
        padding: 8,
      }}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      ListHeaderComponent={<SectionHeader title="Employees" icon={Education} />}
      showsVerticalScrollIndicator={false}
      removeClippedSubviews
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={10}
    />
  );
};

export default ShipPeople;
