import { useEffect, useState, useCallback } from 'react';
import { sendConnectionRequestAPI } from '@/src/networks/connect/connection';
import { fetchShipPeople } from '@/src/networks/ship/people';
import type { FetchPeopleResult } from '@/src/networks/ship/types';
import type { FetchPeopleParams, UseShipPeopleParams, UseShipPeopleReturnI } from './types';

const useShipPeople = ({
  imo,
  dataType,
  pageSize = 10,
  onError,
}: UseShipPeopleParams): UseShipPeopleReturnI => {
  const [people, setPeople] = useState<{ data: FetchPeopleResult[]; total: number }>({
    total: 0,
    data: [],
  });

  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(false);
  const [page, setPage] = useState<number>(0);
  const [connectionStates, setConnectionStates] = useState<
    Record<string, 'idle' | 'loading' | 'success'>
  >({});

  const fetchPeople = useCallback(
    async (pageNumber: number, isRefresh = false): Promise<void> => {
      try {
        if (isRefresh) {
          setRefreshing(true);
          setPage(0);
        } else if (pageNumber > 0) {
          setLoadingMore(true);
        } else {
          setLoading(true);
        }

        const params: FetchPeopleParams = {
          imo,
          dataType,
        };

        const result = await fetchShipPeople({ ...params });

        if (isRefresh || pageNumber === 0) {
          setPeople(result);
        } else {
          setPeople((prev) => ({
            total: result.total,
            data: [...prev.data, ...result.data],
          }));
        }

        setPage(pageNumber);
        setHasMore(result.data.length === pageSize);
      } catch (err) {
        const errorMessage = `Failed to fetch visitors: ${err instanceof Error ? err.message : 'Unknown error'}`;
        if (onError) {
          onError(new Error(errorMessage));
        }
      } finally {
        setLoading(false);
        setLoadingMore(false);
        setRefreshing(false);
      }
    },
    [imo, dataType, pageSize, onError],
  );

  useEffect(() => {
    if (imo && dataType) {
      fetchPeople(0);
    }
  }, [imo, dataType, fetchPeople]);

  const handleRefresh = useCallback((): void => {
    fetchPeople(0, true);
  }, [fetchPeople]);

  const handleLoadMore = useCallback((): void => {
    if (!loadingMore && !loading && hasMore) {
      fetchPeople(page + 1);
    }
  }, [loadingMore, loading, hasMore, page, fetchPeople]);

  const handleConnectRequest = useCallback(
    async (profileId: string) => {
      try {
        setConnectionStates((prev) => ({ ...prev, [profileId]: 'loading' }));

        await sendConnectionRequestAPI({
          receiverProfileId: profileId,
          requestedStatus: 'DISCONNECTED',
        });

        setConnectionStates((prev) => ({ ...prev, [profileId]: 'success' }));
      } catch (error) {
        setConnectionStates((prev) => ({ ...prev, [profileId]: 'idle' }));
        const errorMessage = `Failed to send connection request: ${error instanceof Error ? error.message : 'Unknown error'}`;
        if (onError) {
          onError(new Error(errorMessage));
        }
      }
    },
    [onError],
  );

  return {
    people,
    loading,
    loadingMore,
    hasMore,
    refreshing,
    connectionStates,
    handleRefresh,
    handleLoadMore,
    handleConnectRequest,
  };
};

export default useShipPeople;
