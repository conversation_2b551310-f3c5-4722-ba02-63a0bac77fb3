import type { FetchPeopleResult } from '@/src/networks/ship/types';

export type ShipPeopleProps = {
  imo: string;
  dataType: string;
};

export type FetchPeopleParams = {
  imo: string;
  dataType: string;
};

export type UseShipPeopleParams = {
  imo: string;
  dataType: string;
  pageSize: number;
  onError?: (error: Error) => void;
};

export type UseShipPeopleReturnI = {
  people: { data: FetchPeopleResult[]; total: number };
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  refreshing: boolean;
  connectionStates: Record<string, 'loading' | 'success' | 'idle'>;
  handleRefresh: () => void;
  handleLoadMore: () => void;
  handleConnectRequest: (profileId: string) => Promise<void>;
};
