import React from 'react';
import { Text, View, ScrollView } from 'react-native';
import { twMerge } from 'tailwind-merge';
import { SectionHeader } from '@/src/components/SectionHeader';
import Education from '@/src/assets/svgs/Education';
import Files from '@/src/assets/svgs/Files';
import type { ShipAboutProps } from './types';

const ShipAbout: React.FC<ShipAboutProps> = ({ shipData }) => {
  const shipDetails = [
    { label: 'Name', value: shipData?.name ?? '-' },
    { label: 'Flag', value: shipData?.country?.name ?? '-' },
    { label: 'IMO', value: shipData?.imo ?? '-' },
    { label: 'MMSI', value: shipData?.mmsi ? shipData.mmsi.toString() : '-' },
    { label: 'Call sign', value: shipData?.callSign ?? '-' },
    { label: 'Main vessel type', value: shipData?.mainVesselType?.name ?? '-' },
    {
      label: 'Sub vessel type',
      value:
        shipData?.subVesselTypes && shipData.subVesselTypes.length > 0
          ? shipData.subVesselTypes.map((type) => type?.name ?? '').join(', ')
          : '-',
    },
    { label: 'Year built', value: shipData?.yearBuilt ? shipData.yearBuilt.toString() : '-' },
  ];

  const hasPastNames =
    (shipData?.shipNames?.length ?? 0) > 1 ||
    ((shipData?.shipNames?.length ?? 0) === 1 &&
      shipData?.shipNames?.[0]?.name &&
      shipData?.shipNames?.[0]?.name !== shipData?.name);

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false} bounces={true}>
      <View className="bg-white">
        <SectionHeader containerClassName="px-4" title="Ship details" icon={Education} />
        <View className="flex-1">
          {shipDetails.map((item, index) => (
            <View
              key={index}
              className={`flex-row justify-between px-4 py-3 ${
                index === shipDetails.length - 1
                  ? 'border-none'
                  : 'border-b border-borderGrayExtraLight'
              }`}
            >
              <Text className="text-subLabelGrayDark font-medium text-sm w-1/2">{item.label}</Text>
              <Text className="text-black font-medium text-base text-left w-1/2">{item.value}</Text>
            </View>
          ))}
        </View>
      </View>

      {hasPastNames && (
        <View className="my-2 py-4 bg-white">
          <SectionHeader containerClassName="px-4" title="Past ship names" icon={Files} />
          <View className="px-4 mt-5">
            {shipData?.shipNames
              ?.filter(
                (nameRecord) => nameRecord?.name !== shipData?.name || nameRecord?.toDate !== null,
              )
              ?.map((nameRecord, index, filteredArray) => {
                const fromDate = nameRecord?.fromDate
                  ? new Date(nameRecord.fromDate).toLocaleDateString('en-US', {
                      month: 'short',
                      year: 'numeric',
                    })
                  : 'Unknown';
                const toDate = nameRecord?.toDate
                  ? new Date(nameRecord.toDate).toLocaleDateString('en-US', {
                      month: 'short',
                      year: 'numeric',
                    })
                  : 'Present';
                const period = `${fromDate} - ${toDate}`;

                return (
                  <View
                    key={index}
                    className={twMerge(
                      'py-3',
                      filteredArray.length - 1 === index ? '' : 'border-b border-gray-200',
                    )}
                  >
                    <Text className="text-base font-medium">{nameRecord?.name ?? '-'}</Text>
                    <Text className="text-sm mt-1">{period}</Text>
                  </View>
                );
              })}
          </View>
        </View>
      )}
    </ScrollView>
  );
};

export default ShipAbout;
