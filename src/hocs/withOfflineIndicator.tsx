import type React from 'react';
import OfflineIndicator from '../components/OfflineIndicator';

export function withOfflineIndicator<P extends object>(Component: React.ComponentType<P>) {
  const WrappedComponent = (props: P) => {
    return (
      <OfflineIndicator>
        <Component {...props} />
      </OfflineIndicator>
    );
  };

  WrappedComponent.displayName = `withOfflineIndicator(${Component.displayName || Component.name || 'Component'})`;

  return WrappedComponent;
}
