export type OprTypeE = 'CREATE' | 'UPDATE' | 'DELETE' | 'NESTED_OPR';

export type DesignationI = {
  id: string;
  name: string;
  dataType: 'raw' | 'master' | undefined;
};

export type ExperienceDesignationI = {
  dataType?: 'raw' | 'master' | undefined;
  experienceDesignationId?: string;
  fromDate?: string;
  toDate?: string;
  designation: DesignationI;
  id?: string;
  name: string;
  from?: string;
  to?: string;
};

export type ExperienceDesignationSchema = {
  id?: string;
  title: string;
  from: string;
  to?: string;
  description?: string;
  isCurrent?: boolean;
};

export type ExperienceItemI = {
  id: string;
  entity: {
    dataType?: 'raw' | 'master' | undefined;
    id: string;
    name: string;
    logo?: string;
    website?: string;
    location?: string;
  };
  years: number;
  months: number;
  dataType?: 'raw' | 'master' | undefined;
  designations?: ExperienceDesignationI[];
};

type CountryI = {
  iso2: string;
  name: string;
};

export type PortI = {
  unLocode: string;
  name: string;
  country: CountryI;
};

export type ExperienceFetchParamsI = {
  profileId: string;
};

export interface ExperienceFetchForClientResultI extends ExperienceItemI {}

export type ExperienceFetchReponseI = {
  data: ExperienceFetchForClientResultI[];
  total: number;
  portVisits: { data: PortI[]; total: number };
};

export type ExperienceModuleCreateOneParamsI = {
  opr?: OprTypeE;
  id?: string;
  entity?: {
    opr?: OprTypeE;
    id: string;
    name?: string;
    logo?: string;
    website?: string;
    location?: string;
  };
  designations?: {
    opr?: OprTypeE;
    id?: string;
    fromDate?: string;
    designation: {
      opr?: OprTypeE;
      id?: string;
      dataType?: string;
    };
  }[];
};

export type ExperienceStateI = {
  experiences: ExperienceFetchReponseI;
  profileId?: string;
  loading: boolean;
  error: string | null;
};

export enum ExperienceActionTypes {
  FETCH_EXPERIENCES = 'FETCH_EXPERIENCES',
  ADD_EXPERIENCE = 'ADD_EXPERIENCE',
  UPDATE_EXPERIENCE = 'UPDATE_EXPERIENCE',
  DELETE_EXPERIENCE = 'DELETE_EXPERIENCE',
}

export type ExperienceModuleUpdateOneParamsI = {
  opr?: OprTypeE;
  id?: string;
  entity?: {
    id: string;
    dataType: 'raw' | 'master' | undefined;
  };
  designations?: {
    opr?: OprTypeE;
    id?: string;
    fromDate?: string;
    toDate?: string;
    designation?: {
      id?: string;
      opr?: OprTypeE;
      dataType?: string;
    };
  }[];
}[];
