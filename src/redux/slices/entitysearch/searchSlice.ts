import { createAsyncThunk, createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { SearchResultI, SearchState } from './types';

const initialState: SearchState = {
  selections: {},
  multipleSelections: {},
};

export const clearSelectionAsync = createAsyncThunk(
  'search/clearSelectionAsync',
  async (key: string, { dispatch }) => {
    dispatch(clearSelection(key));
  },
);

const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    setSelection: (state, action: PayloadAction<{ key: string; value: SearchResultI }>) => {
      const { key, value } = action.payload;
      state.selections[key] = value;
    },
    setMultipleSelections: (
      state,
      action: PayloadAction<{ key: string; value: SearchResultI[] }>,
    ) => {
      const { key, value } = action.payload;
      state.multipleSelections[key] = value;
    },
    clearSelection: (state, action: PayloadAction<string>) => {
      delete state.selections[action.payload];
    },
    clearMultipleSelections: (state, action: PayloadAction<string>) => {
      delete state.multipleSelections[action.payload];
    },
    addToMultipleSelection: (
      state,
      action: PayloadAction<{ key: string; value: SearchResultI }>,
    ) => {
      const { key, value } = action.payload;
      if (!state.multipleSelections[key]) {
        state.multipleSelections[key] = [];
      }
      state.multipleSelections[key].push(value);
    },
    removeFromMultipleSelection: (state, action: PayloadAction<{ key: string; id: string }>) => {
      const { key, id } = action.payload;
      if (state.multipleSelections[key]) {
        state.multipleSelections[key] = state.multipleSelections[key].filter(
          (item) => item.id !== id,
        );
      }
    },
    clearAllSelections: (state) => {
      state.selections = {};
      state.multipleSelections = {};
    },
  },
});

export const {
  setSelection,
  clearSelection,
  setMultipleSelections,
  clearMultipleSelections,
  addToMultipleSelection,
  removeFromMultipleSelection,
  clearAllSelections,
} = searchSlice.actions;

export default searchSlice.reducer;
