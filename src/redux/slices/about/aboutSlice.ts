import { createAsyncThunk, createSlice, type PayloadAction } from '@reduxjs/toolkit';
import {
  fetchStatutoryCertificationsAPI,
  fetchValueAddedCertificationsAPI,
} from '@/src/networks/career/certification';
import { fetchIdentityDocumentsAPI, fetchVisaDocumentsAPI } from '@/src/networks/career/document';
import { fetchEducationsAPI } from '@/src/networks/career/education';
import { RootState } from '../../store';
import { AboutState, ProfileStatutoryI, SearchResultTypesI, VisaI } from './types';

const initialState: AboutState = {
  description: null,
  educationCount: 0,
  statutoryCount: 0,
  valueAddedCount: 0,
  identityCount: 0,
  visaCount: 0,
  maritimeSkillsCount: 0,
  otherSkillsCount: 0,
  profileEducations: [],
  profileStatutoryCertifications: [],
  profileIdentityDocuments: [],
  profileMaritimeSkills: [],
  educations: [],
  educationsPagination: {
    page: 0,
    pageSize: 10,
    hasMore: true,
    loading: false,
  },
  statutoryCertifications: [],
  statutoryCertificationsPagination: {
    page: 0,
    pageSize: 10,
    hasMore: true,
    loading: false,
  },
  valueAddedCertifications: [],
  valueAddedCertificationsPagination: {
    page: 0,
    pageSize: 10,
    hasMore: true,
    loading: false,
  },
  identityDocuments: [],
  identityDocumentsPagination: {
    page: 0,
    pageSize: 10,
    hasMore: true,
    loading: false,
  },
  visaDocuments: [],
  visaDocumentsPagination: {
    page: 0,
    pageSize: 10,
    hasMore: true,
    loading: false,
  },
};

const createFetchThunk = (
  name: string,
  apiCall: (profileId: string, page: number, pageSize: number) => unknown,
) =>
  createAsyncThunk(
    name,
    async (
      {
        profileId,
        page = 0,
        refresh = false,
      }: { profileId: string; page?: number; refresh?: boolean },
      { getState, rejectWithValue },
    ) => {
      try {
        const state = getState() as RootState;
        const paginationKey = (name.split('/')[1] + 'Pagination') as keyof AboutState;
        const { pageSize } = state.about[paginationKey] as { pageSize: number };
        const response = await apiCall(profileId, page, pageSize);
        return { response, refresh };
      } catch (error) {
        return rejectWithValue(`Failed to fetch ${name.split('/')[1]}`);
      }
    },
  );

export const fetchEducations = createFetchThunk('career/educations', fetchEducationsAPI);
export const fetchStatutoryCertifications = createFetchThunk(
  'career/statutoryCertifications',
  fetchStatutoryCertificationsAPI,
);
export const fetchValueAddedCertifications = createFetchThunk(
  'career/valueAddedCertifications',
  fetchValueAddedCertificationsAPI,
);
export const fetchIdentityDocuments = createFetchThunk(
  'career/identityDocuments',
  fetchIdentityDocumentsAPI,
);
export const fetchVisaDocuments = createFetchThunk('career/visaDocuments', fetchVisaDocumentsAPI);

const createEducation = (payload: {
  id: string;
  degree: SearchResultTypesI;
  institution: SearchResultTypesI;
  fromDate: string;
  toDate: string;
}) => ({
  id: payload.id,
  degree: payload.degree,
  entity: payload.institution,
  fromDate: payload.fromDate,
  toDate: payload.toDate,
});

const createCertification = (payload: {
  id: string;
  provider: { id: string; name: string };
  course: { id: string; name: string };
  validFrom: string;
  validUntil: string;
}) => ({
  id: payload.id,
  entity: {
    id: payload.provider.id,
    name: payload.provider.name,
  },
  certificateCourse: {
    id: payload.course.id,
    name: payload.course.name,
  },
  fromDate: payload.validFrom,
  untilDate: payload.validUntil,
});

const createIdentityDocument = (payload: {
  id: string;
  country: { name: string };
  expiryStatus: string;
  validFrom: string;
  validUntil: string;
  documentName: string;
}) => ({
  id: payload.id,
  country: {
    name: payload.country.name,
  },
  expiryStatus: payload.expiryStatus,
  fromDate: payload.validFrom,
  untilDate: payload.validUntil,
  type: payload.documentName === "CDC (Seamen's Book)" ? 'CDC' : payload.documentName.toUpperCase(),
});

const createProfileIdentityDocument = (payload: {
  id: string;
  country: { name: string };
  validFrom: string;
  validUntil: string;
  documentName: string;
}) => ({
  id: payload.id,
  countryName: payload.country.name,
  fromDate: payload.validFrom,
  untilDate: payload.validUntil,
  type: payload.documentName === "CDC (Seamen's Book)" ? 'CDC' : payload.documentName.toUpperCase(),
});

const createVisaDocument = (payload: {
  id: string;
  country: { name: string };
  expiryStatus: string;
  validFrom: string;
  validUntil: string;
  fileUrl: string;
  documentName: string;
}) => ({
  id: payload.id,
  country: {
    name: payload.country.name,
  },
  expiryStatus: payload.expiryStatus,
  fromDate: payload.validFrom,
  untilDate: payload.validUntil,
  fileUrl: payload.fileUrl,
  name: payload.documentName,
});

const updateArrayItem = <T extends { id: string }>(array: T[], id: string, newItem: T): T[] => {
  const index = array.findIndex((item) => item.id === id);
  return index !== -1 ? [...array.slice(0, index), newItem, ...array.slice(index + 1)] : array;
};

const removeArrayItem = <T extends { id: string }>(array: T[], id: string): T[] =>
  array.filter((item) => item.id !== id);

const aboutSlice = createSlice({
  name: 'about',
  initialState,
  reducers: {
    setAboutData: (state, action) => {
      const {
        educationCount,
        statutoryCertCount,
        valueAddedCertCount,
        identityCount,
        visaCount,
        maritimeSkillsTotal,
      } = action.payload.meta;

      Object.assign(state, {
        educationCount,
        statutoryCount: statutoryCertCount,
        valueAddedCount: valueAddedCertCount,
        identityCount,
        visaCount,
        maritimeSkillsCount: maritimeSkillsTotal,
        profileEducations: action.payload.institutions,
        profileStatutoryCertifications: action.payload.statutoryCerts,
        profileIdentityDocuments: action.payload.identities,
        profileMaritimeSkills: action.payload.maritimeSkills,
        description: action.payload.description,
      });
    },

    incrementMaritimeSkillsCount: (state) => {
      state.maritimeSkillsCount += 1;
    },

    addEducation: (state, action) => {
      const education = createEducation(action.payload);
      state.educations.push(education);
      state.educationCount += 1;
      if (state.profileEducations.length < 3) {
        state.profileEducations.push(education);
      }
    },

    deleteEducation: (state, action) => {
      const id = action.payload;
      state.profileEducations = removeArrayItem(state.profileEducations, id);
      state.educations = removeArrayItem(state.educations, id);
      if (state.educationCount > 0) state.educationCount -= 1;
    },

    editEducation: (state, action) => {
      const education = createEducation({ ...action.payload, id: action.payload.educationId });
      state.educations = updateArrayItem(state.educations, action.payload.educationId, education);
      state.profileEducations = updateArrayItem(
        state.profileEducations,
        action.payload.educationId,
        education,
      );
    },

    addStatutoryCertification: (state, action) => {
      const certification = createCertification(action.payload);
      state.statutoryCertifications.push(certification);
      state.statutoryCount += 1;
      if (state.profileStatutoryCertifications.length < 3) {
        state.profileStatutoryCertifications.push(certification as unknown as ProfileStatutoryI);
      }
    },

    editStatutoryCertification: (state, action) => {
      const certification = createCertification({
        ...action.payload,
        id: action.payload.certificationId,
      });
      state.statutoryCertifications = updateArrayItem(
        state.statutoryCertifications,
        action.payload.certificationId,
        certification,
      );
      state.profileStatutoryCertifications = updateArrayItem(
        state.profileStatutoryCertifications,
        action.payload.certificationId,
        certification as unknown as ProfileStatutoryI,
      );
    },

    deleteStatutoryCertification: (state, action) => {
      const id = action.payload;
      state.profileStatutoryCertifications = removeArrayItem(
        state.profileStatutoryCertifications,
        id,
      );
      state.statutoryCertifications = removeArrayItem(state.statutoryCertifications, id);
      if (state.statutoryCount > 0) state.statutoryCount -= 1;
    },

    addValueAddedCertification: (state, action) => {
      const certification = createCertification(action.payload);
      state.valueAddedCertifications.push(certification);
      state.valueAddedCount += 1;
    },

    editValueAddedCertification: (state, action) => {
      const certification = createCertification({
        ...action.payload,
        id: action.payload.certificationId,
      });
      state.valueAddedCertifications = updateArrayItem(
        state.valueAddedCertifications,
        action.payload.certificationId,
        certification,
      );
    },

    deleteValueAddedCertification: (state, action) => {
      state.valueAddedCertifications = removeArrayItem(
        state.valueAddedCertifications,
        action.payload,
      );
      if (state.valueAddedCount > 0) state.valueAddedCount -= 1;
    },

    addIdentityDocument: (state, action) => {
      const document = createIdentityDocument(action.payload);
      state.identityDocuments.push(document);
      state.identityCount += 1;

      if (state.profileIdentityDocuments.length < 3) {
        const profileDocument = createProfileIdentityDocument(action.payload);
        state.profileIdentityDocuments.push(profileDocument);
      }
    },

    editIdentityDocument: (state, action) => {
      const document = createIdentityDocument({ ...action.payload, id: action.payload.documentId });
      state.identityDocuments = updateArrayItem(
        state.identityDocuments,
        action.payload.documentId,
        document,
      );

      const profileDocument = createProfileIdentityDocument({
        ...action.payload,
        id: action.payload.documentId,
      });
      state.profileIdentityDocuments = updateArrayItem(
        state.profileIdentityDocuments,
        action.payload.documentId,
        profileDocument,
      );
    },

    deleteIdentityDocument: (state, action) => {
      const id = action.payload;
      state.profileIdentityDocuments = removeArrayItem(state.profileIdentityDocuments, id);
      state.identityDocuments = removeArrayItem(state.identityDocuments, id);
      if (state.identityCount > 0) state.identityCount -= 1;
    },

    addVisaDocument: (state, action) => {
      const document = createVisaDocument(action.payload);
      state.visaDocuments.push(document);
      state.visaCount += 1;
    },

    editVisaDocument: (state, action) => {
      const document = {
        id: action.payload.documentId,
        country: { name: action.payload.country.name },
        fromDate: action.payload.validFrom,
        untilDate: action.payload.validUntil,
        type:
          action.payload.documentName === "CDC (Seamen's Book)"
            ? 'CDC'
            : action.payload.documentName.toUpperCase(),
      };
      state.visaDocuments = updateArrayItem(
        state.visaDocuments,
        action.payload.documentId,
        document as unknown as VisaI,
      );
    },

    deleteVisaDocument: (state, action) => {
      state.visaDocuments = removeArrayItem(state.visaDocuments, action.payload);
      if (state.visaCount > 0) state.visaCount -= 1;
    },

    updateDescription: (state, action: PayloadAction<string>) => {
      state.description = action.payload;
    },
  },

  extraReducers: (builder) => {
    const handleFulfilled =
      (stateKey: keyof AboutState) =>
      (state: { [x: string]: any }, action: { payload: { response: any; refresh: unknown } }) => {
        const { response, refresh } = action.payload;

        if (refresh) {
          state[stateKey] = response;
        } else {
          const existingIds = new Set(state[stateKey].map((item: { id: string }) => item.id));
          const newItems = response.filter((item: { id: string }) => !existingIds.has(item.id));
          state[stateKey] = [...state[stateKey], ...newItems];
        }
      };

    builder
      .addCase(fetchEducations.fulfilled, handleFulfilled('educations'))
      .addCase(fetchStatutoryCertifications.fulfilled, handleFulfilled('statutoryCertifications'))
      .addCase(fetchValueAddedCertifications.fulfilled, handleFulfilled('valueAddedCertifications'))
      .addCase(fetchIdentityDocuments.fulfilled, handleFulfilled('identityDocuments'))
      .addCase(fetchVisaDocuments.fulfilled, handleFulfilled('visaDocuments'));
  },
});

export const {
  setAboutData,
  incrementMaritimeSkillsCount,
  addEducation,
  editEducation,
  deleteEducation,
  addStatutoryCertification,
  editStatutoryCertification,
  deleteStatutoryCertification,
  addValueAddedCertification,
  editValueAddedCertification,
  deleteValueAddedCertification,
  addIdentityDocument,
  editIdentityDocument,
  deleteIdentityDocument,
  addVisaDocument,
  editVisaDocument,
  deleteVisaDocument,
  updateDescription,
} = aboutSlice.actions;

export default aboutSlice.reducer;
