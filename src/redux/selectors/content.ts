/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSelector } from '@reduxjs/toolkit';
import { PostExternalClientI } from '@/src/networks/content/types';
import { ScrapBookPostFetchForClientI } from '@/src/networks/port/types';
import { ContentState, PaginationState, ScrapbookPaginationState } from '../slices/content/types';
import { selectCurrentUser } from './user';

export const selectContentState = (state: { content: ContentState }): ContentState => state.content;

export const selectAllPosts = createSelector(
  selectContentState,
  (contentState): PostExternalClientI[] => contentState.posts || [],
);

export const selectPost = createSelector(
  selectContentState,
  (contentState): PostExternalClientI | null => contentState.post || null,
);

export const selectPostById = createSelector(
  [selectAllPosts, (_, postId: string) => postId],
  (posts, postId): PostExternalClientI | undefined => posts.find((post) => post.id === postId),
);

export const selectPagination = createSelector(
  selectContentState,
  (contentState): PaginationState => contentState.pagination,
);

export const selectHasMorePosts = createSelector(
  selectPagination,
  (pagination): boolean => pagination.hasMore,
);

export const selectLatestPosts = createSelector(selectAllPosts, (posts): PostExternalClientI[] =>
  [...posts].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()),
);

export const selectOwnPosts = createSelector(
  selectContentState,
  (contentState): PostExternalClientI[] => contentState.ownPosts || [],
);

export const selectOwnPostsPagination = createSelector(
  selectContentState,
  (contentState): Omit<PaginationState, 'otherCursorId'> => contentState.ownPostsPagination,
);

export const selectHasMoreOwnPosts = createSelector(
  selectOwnPostsPagination,
  (pagination): boolean => pagination.hasMore,
);

export const selectProfilePosts = createSelector(
  [selectContentState, selectCurrentUser, (_, profileId: string) => profileId],
  (contentState, userState, profileId): PostExternalClientI[] => {
    if (userState?.profileId === profileId) {
      return contentState.ownPosts || [];
    }
    return contentState.profilePosts[profileId]?.posts || [];
  },
);

export const selectProfilePostsPagination = createSelector(
  [selectContentState, selectCurrentUser, (_, profileId: string) => profileId],
  (contentState, userState, profileId): Omit<PaginationState, 'otherCursorId'> => {
    if (userState?.profileId === profileId) {
      return contentState.ownPostsPagination;
    }
    return (
      contentState.profilePosts[profileId]?.pagination || {
        cursorId: null,
        hasMore: true,
      }
    );
  },
);

export const selectHasMoreProfilePosts = createSelector(
  [selectProfilePostsPagination],
  (pagination): boolean => pagination.hasMore,
);

export const selectReactionsByPostId = createSelector(
  [selectContentState, (_, postId: string) => postId],
  (contentState, postId) => contentState.reactions[postId] || [],
);

export const selectCommentsByPostId = createSelector(
  [selectContentState, (_, postId: string) => postId],
  (contentState, postId) => contentState.comments[postId] || { comments: [] },
);

export const selectPostReactionsCount = createSelector(
  [selectPostById],
  (post): number => post?.reactionsCount || 0,
);

export const selectPostCommentsCount = createSelector(
  [selectPostById],
  (post): number => post?.totalCommentsCount || 0,
);

export const selectIsCurrentUserPost = createSelector(
  [
    (state, postId: string) => selectAnyPostById(state, postId),
    (state) => selectCurrentUser(state),
  ],
  (post, currentUser): boolean => {
    if (!post || !currentUser?.profileId) return false;
    return post.Profile.id === currentUser.profileId;
  },
);

export const selectAnyPostById = createSelector(
  [selectContentState, (_, postId: string) => postId],
  (contentState, postId): PostExternalClientI | undefined => {
    const homePost = contentState.posts.find((post) => post.id === postId);
    if (homePost) return homePost;

    const ownPost = contentState.ownPosts.find((post) => post.id === postId);
    if (ownPost) return ownPost;

    for (const profileId in contentState.profilePosts) {
      if (contentState.profilePosts[profileId]) {
        const profilePosts = contentState.profilePosts[profileId].posts;
        const foundPost = profilePosts.find((post) => post.id === postId);
        if (foundPost) return foundPost;
      }
    }

    return contentState.deletedPosts[postId]?.post;
  },
);

export const selectDeletedPostById = createSelector(
  [selectContentState, (_, postId: string) => postId],
  (contentState, postId) => contentState.deletedPosts[postId]?.post,
);

export const selectScrapbookPosts = createSelector(
  selectContentState,
  (contentState): ScrapBookPostFetchForClientI[] => contentState.scrapbookPosts || [],
);

export const selectScrapbookPostById = createSelector(
  [selectScrapbookPosts, (_, postId: string) => postId],
  (scrapbookPosts, postId): ScrapBookPostFetchForClientI | undefined =>
    scrapbookPosts.find((post) => post.id === postId),
);

export const selectScrapbookPagination = createSelector(
  selectContentState,
  (contentState): ScrapbookPaginationState => contentState.scrapbookPagination,
);

export const selectHasMoreScrapbookPosts = createSelector(
  selectScrapbookPagination,
  (pagination): boolean => pagination.hasMore,
);

export const selectScrapbookCommentsByPostId = createSelector(
  [selectContentState, (_, postId: string) => postId],
  (contentState, postId) => contentState.scrapbookComments[postId] || { comments: [] },
);

export const selectScrapbookCommentRepliesByCommentId = createSelector(
  [selectContentState, (_, commentId: string) => commentId],
  (contentState, commentId) => contentState.scrapbookCommentReplies[commentId] || { comments: [] },
);

export const selectDeletedScrapbookPostById = createSelector(
  [selectContentState, (_, postId: string) => postId],
  (contentState, postId) => contentState.deletedScrapbookPosts[postId]?.post,
);

export const selectIsCurrentUserScrapbookPost = createSelector(
  [
    (state, postId: string) => selectScrapbookPostById(state, postId),
    (state) => selectCurrentUser(state),
  ],
  (post, currentUser): boolean => {
    if (!post || !currentUser?.profileId) return false;
    return post.profile.id === currentUser.profileId;
  },
);

export const selectScrapbookPostReactionsCount = createSelector(
  [selectScrapbookPostById],
  (post): number => post?.reactionCount || 0,
);

export const selectScrapbookPostCommentsCount = createSelector(
  [selectScrapbookPostById],
  (post): number => post?.commentCount || 0,
);

export const selectScrapbookReactionsByPostId = createSelector(
  [selectContentState, (_, postId: string) => postId],
  (contentState, postId) => contentState.scrapbookReactions?.[postId] || { data: [], total: 0 },
);

export const selectSearchPostsByTerm = createSelector(
  [selectContentState, (_, searchTerm: string) => searchTerm],
  (contentState, searchTerm): PostExternalClientI[] => contentState.searchPosts?.[searchTerm] || [],
);

export const selectAllSearchPosts = createSelector(
  selectContentState,
  (contentState): Record<string, PostExternalClientI[]> => contentState.searchPosts || {},
);

export const selectCachedPosts = createSelector(
  selectContentState,
  (contentState) => contentState.cachedPosts || []
);
