import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';

export const selectAboutState = (state: RootState) => state.about;

export const selectAboutProfileEducations = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.profileEducations,
);

export const selectAboutProfileEducationCount = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.educationCount,
);

export const selectAboutProfileStatutoryCertifications = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.profileStatutoryCertifications,
);

export const selectAboutProfileCertificationsStatutoryCount = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.statutoryCount,
);

export const selectAboutProfileCertificationsValueAddedCount = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.valueAddedCount,
);

export const selectAboutProfileIdentityDocuments = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.profileIdentityDocuments,
);

export const selectAboutProfileDocumentsIdentityCount = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.identityCount,
);

export const selectAboutProfileDocumentsVisaCount = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.visaCount,
);

export const selectAboutProfileMaritimeSkills = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.profileMaritimeSkills,
);

export const selectAboutProfileMaritimeSkillsCount = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.maritimeSkillsCount,
);

export const selectEducations = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.educations,
);

export const selectStatutoryCertifications = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.statutoryCertifications,
);

export const selectValueAddedCertifications = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.valueAddedCertifications,
);

export const selectIdentityDocuments = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.identityDocuments,
);

export const selectVisaDocuments = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.visaDocuments,
);

export const selectProfileDescription = createSelector(
  [selectAboutState],
  (aboutState) => aboutState.description,
);
