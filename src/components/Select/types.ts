/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export interface IdTitleI {
  id: string | number;
  title: string;
}

export interface SelectProps {
  options: IdTitleI[];
  value?: string | number;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  className?: string;
  dropdownClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
}

export interface SelectHandleI {
  toggle: () => void;
  close: () => void;
}
