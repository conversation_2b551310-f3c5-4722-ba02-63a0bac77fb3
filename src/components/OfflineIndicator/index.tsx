import React, { useEffect, useState } from 'react';
import { View, Text, Pressable, Animated, Platform } from 'react-native';
import { addEventListener } from '@react-native-community/netinfo';
import type { OfflineIndicatorProps } from './types';

const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(true);
  const [slideAnim] = useState(new Animated.Value(-100));
  const [showRetry, setShowRetry] = useState(false);
  const [connectionType, setConnectionType] = useState<string | null>(null);
  const [isInternetReachable, setIsInternetReachable] = useState<boolean | null>(null);

  useEffect(() => {
    const unsubscribe = addEventListener((state) => {
      const connected = state.isConnected && state.isInternetReachable;
      setIsConnected(connected ?? false);
      setConnectionType(state.type);
      setIsInternetReachable(state.isInternetReachable);

      if (!connected) {
        setShowRetry(false);
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }).start();

        setTimeout(() => setShowRetry(true), 2000);
      } else {
        Animated.spring(slideAnim, {
          toValue: -100,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }).start();
      }
    });

    return () => unsubscribe();
  }, [slideAnim]);

  const handleRetry = async () => {
    setShowRetry(false);

    const NetInfo = await import('@react-native-community/netinfo');
    const state = await NetInfo.fetch();
    const connected = state.isConnected && state.isInternetReachable;

    setIsConnected(connected ?? false);
    setConnectionType(state.type);
    setIsInternetReachable(state.isInternetReachable);

    if (!connected) {
      setTimeout(() => setShowRetry(true), 1000);
    }
  };

  const getConnectionStatus = () => {
    if (isConnected) return 'Connected';
    if (connectionType === 'none') return 'No Connection';
    if (isInternetReachable === false) return 'Limited Connection';
    return 'Disconnected';
  };

  const getConnectionColor = () => {
    if (isConnected) return 'bg-green-400';
    if (connectionType === 'none') return 'bg-red-400';
    if (isInternetReachable === false) return 'bg-yellow-400';
    return 'bg-red-400';
  };

  const getConnectionTextColor = () => {
    if (isConnected) return 'text-green-500';
    if (connectionType === 'none') return 'text-red-500';
    if (isInternetReachable === false) return 'text-yellow-600';
    return 'text-red-500';
  };

  if (isConnected) {
    return <>{children}</>;
  }

  return (
    <View className="flex-1 bg-gray-50">
      <Animated.View
        style={{
          transform: [{ translateY: slideAnim }],
          position: 'absolute',
          top: Platform.OS === 'ios' ? 50 : 30,
          left: 0,
          right: 0,
          zIndex: 1000,
        }}
        className="mx-4 bg-red-500 rounded-xl shadow-lg mt-10"
      >
        <View className="px-4 py-3 flex-row items-center justify-between">
          <View className="flex-row items-center flex-1">
            <View className="w-2 h-2 bg-white rounded-full mr-3" />
            <Text className="text-white font-medium text-sm">No internet connection</Text>
          </View>
          {showRetry && (
            <Pressable
              onPress={handleRetry}
              className="bg-white/20 px-3 py-1 rounded-lg active:bg-white/30"
            >
              <Text className="text-white text-xs font-medium">Retry</Text>
            </Pressable>
          )}
        </View>
      </Animated.View>

      <View className="flex-1 items-center justify-center px-8">
        <View className="w-32 h-32 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full items-center justify-center mb-8 shadow-sm">
          <View className="w-20 h-20 bg-white rounded-full items-center justify-center">
            <Text className="text-4xl">📡</Text>
          </View>
        </View>

        <View className="items-center mb-8">
          <Text className="text-2xl font-bold text-gray-900 text-center mb-3">You're offline</Text>
          <Text className="text-base text-gray-600 text-center leading-6 max-w-sm">
            Check your internet connection and try again. Some features may not be available.
          </Text>
        </View>

        <View className="w-full max-w-sm space-y-3 mb-8">
          <View className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className={`w-3 h-3 ${getConnectionColor()} rounded-full mr-3`} />
                <Text className="text-gray-700 font-medium">Internet</Text>
              </View>
              <Text className={`${getConnectionTextColor()} text-sm font-medium`}>
                {getConnectionStatus()}
              </Text>
            </View>
          </View>

          {connectionType && (
            <View className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <View className="w-3 h-3 bg-blue-400 rounded-full mr-3" />
                  <Text className="text-gray-700 font-medium">Connection Type</Text>
                </View>
                <Text className="text-blue-600 text-sm font-medium capitalize">
                  {connectionType === 'cellular' ? 'Mobile Data' : connectionType}
                </Text>
              </View>
            </View>
          )}

          <View className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className="w-3 h-3 bg-yellow-400 rounded-full mr-3" />
                <Text className="text-gray-700 font-medium">App Data</Text>
              </View>
              <Text className="text-yellow-600 text-sm font-medium">Limited</Text>
            </View>
          </View>
        </View>

        <Pressable
          onPress={handleRetry}
          className="bg-green-600 rounded-xl py-4 px-8 shadow-lg active:bg-green-700 mb-4"
        >
          <Text className="text-white font-semibold text-lg">Try Again</Text>
        </Pressable>

        <View className="bg-green-50 rounded-xl p-4 border border-green-100">
          <Text className="text-green-800 font-medium text-center mb-2">Troubleshooting tips:</Text>
          <View className="space-y-1">
            <Text className="text-green-700 text-sm text-center">
              • Check your WiFi or mobile data
            </Text>
            <Text className="text-green-700 text-sm text-center">
              • Move to an area with better signal
            </Text>
            <Text className="text-green-700 text-sm text-center">
              • Restart your device if needed
            </Text>
            {connectionType === 'cellular' && (
              <Text className="text-green-700 text-sm text-center">
                • Try switching to WiFi if available
              </Text>
            )}
            {connectionType === 'wifi' && (
              <Text className="text-green-700 text-sm text-center">
                • Try switching to mobile data
              </Text>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

export default OfflineIndicator;
