/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export interface CustomModalProps {
  isVisible: boolean;
  title: string;
  description?: string;
  cancelText?: string;
  confirmText?: string;
  onConfirm: (inputValue?: string) => void;
  onCancel: () => void;
  inputPlaceholder?: string;
  inputValue?: string;
  onInputChange?: (text: string) => void;
  inputType?: 'default' | 'number' | 'email' | 'password';
  inputRequired?: boolean;
  inputLabel?: string;
  bodyComponent?: React.ReactNode;
  isConfirming?: boolean;
  confirmButtonVariant?: 'default' | 'danger';
}
