import { IdNameI, IdTitleI } from '@/src/types/common/data';

export interface ChipInputProps {
  title?: string;
  placeholder?: string;
  chips?: IdNameI[];
  onRemove?: (id: string | number) => void;
  onAdd?: (value: string) => void;
  className?: string;
  maxLength?: number;
  disabled?: boolean;
  error?: string;
  titleClassName?: string;
  removable?: boolean;
  showTitle?: boolean;
  showBorder?: boolean;
}
