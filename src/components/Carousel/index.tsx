/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { useEffect, useState } from 'react';
import { Dimensions, PanResponder, Pressable, View, ViewStyle, Text } from 'react-native';
import Animated, {
  runOnJS,
  useAnimatedReaction,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { twMerge } from 'tailwind-merge';
import ChevronLeft from '@/src/assets/svgs/ChevronLeft';
import ChevronRight from '@/src/assets/svgs/ChevronRight';
import { CarouselProps } from './types';

const { width } = Dimensions.get('window');

const Carousel = ({
  children,
  showArrows = true,
  showDots = true,
  showSlideNumbers = false,
  autoPlay = false,
  duration = 5000,
  className,
  dotClassName,
  arrowClassName,
  activeColor = '#1B4332',
  inactiveColor = '#D1FAE5',
  initialIndex = 0,
}: CarouselProps): React.JSX.Element => {
  if (!children || children.length === 0) {
    return <View className={twMerge('relative w-full overflow-hidden', className)} />;
  }

  const safeInitialIndex = Math.max(0, Math.min(initialIndex, children.length - 1));
  const [currentIndex, setCurrentIndex] = useState<number>(safeInitialIndex);
  const translateX = useSharedValue<number>(-safeInitialIndex * width);
  const isPanning = useSharedValue<boolean>(false);

  const activeIndex = useDerivedValue(() => Math.round(Math.abs(translateX.value) / width));

  useAnimatedReaction(
    () => activeIndex.value,
    (value) => {
      runOnJS(setCurrentIndex)(value);
    },
  );

  const goToSlide = (index: number): void => {
    translateX.value = withTiming(-index * width);
  };

  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;

    if (autoPlay && !isPanning.value) {
      interval = setInterval(() => {
        const nextIndex = (currentIndex + 1) % children.length;
        goToSlide(nextIndex);
      }, duration);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [autoPlay, duration, children.length, isPanning.value, currentIndex]);

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      isPanning.value = true;
    },
    onPanResponderMove: (_, { dx }) => {
      const basePosition = -currentIndex * width;
      let newPosition = basePosition + dx;

      const minPosition = -(children.length - 1) * width;
      const maxPosition = 0;

      if (newPosition > maxPosition) {
        const overscroll = newPosition - maxPosition;
        newPosition = maxPosition + overscroll * 0.3;
      } else if (newPosition < minPosition) {
        const overscroll = minPosition - newPosition;
        newPosition = minPosition - overscroll * 0.3;
      }

      translateX.value = newPosition;
    },
    onPanResponderRelease: (_, { dx, vx }) => {
      isPanning.value = false;

      let newIndex: number;

      if (Math.abs(dx) > width / 3 || Math.abs(vx) > 0.5) {
        newIndex =
          dx > 0 ? Math.max(0, currentIndex - 1) : Math.min(children.length - 1, currentIndex + 1);
      } else {
        newIndex = currentIndex;
      }

      translateX.value = withSpring(-newIndex * width, {
        velocity: vx,
        damping: 20,
      });
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const dotStyle = (index: number): ViewStyle => ({
    width: 25,
    height: 8,
    borderRadius: 24,
    backgroundColor: currentIndex === index ? activeColor : inactiveColor,
  });

  return (
    <View className={twMerge('relative w-full overflow-hidden', className)}>
      <Animated.View className="flex-row" style={animatedStyle} {...panResponder.panHandlers}>
        {children.map((child, index) => (
          <View key={index} style={{ width }}>
            {child}
          </View>
        ))}
      </Animated.View>

      {showArrows && (
        <>
          <Pressable
            onPress={() => goToSlide(Math.max(0, currentIndex - 1))}
            className={twMerge(
              'absolute left-4 top-1/2 -translate-y-1/2 rounded-full bg-white/80 p-2 shadow-md',
              arrowClassName,
            )}
          >
            <ChevronLeft />
          </Pressable>
          <Pressable
            onPress={() => goToSlide(Math.min(children.length - 1, currentIndex + 1))}
            className={twMerge(
              'absolute right-4 top-1/2 -translate-y-1/2 rounded-full bg-white/80 p-2 shadow-md',
              arrowClassName,
            )}
          >
            <ChevronRight />
          </Pressable>
        </>
      )}

      {showSlideNumbers && (
        <View className="absolute bottom-4 right-4 bg-black/50 px-3 py-1 rounded-full">
          <Text className="text-white font-medium">
            {currentIndex + 1}/{children.length}
          </Text>
        </View>
      )}

      {showDots && (
        <View className="flex-row gap-4 mt-14 mx-auto">
          {children.map((_, index) => (
            <Pressable
              key={index}
              onPress={() => goToSlide(index)}
              style={dotStyle(index)}
              className={twMerge('transition-all duration-300', dotClassName)}
            />
          ))}
        </View>
      )}
    </View>
  );
};

export default Carousel;
