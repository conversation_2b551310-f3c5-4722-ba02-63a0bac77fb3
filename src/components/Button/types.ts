/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ReactNode } from 'react';
import { VARIANTS } from './consts';

export type ButtonVariant = keyof typeof VARIANTS;

export interface ButtonProps {
  onPress: () => void;
  variant?: ButtonVariant;
  label: string;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  labelClassName?: string;
  prefixIcon?: ReactNode;
  suffixIcon?: ReactNode;
}
