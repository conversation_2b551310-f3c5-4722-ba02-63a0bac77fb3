/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export const VARIANTS = {
  primary: {
    container: 'bg-green-700 hover:bg-green-800 text-white rounded px-4 py-2',
    label: 'text-white',
    spinnerColor: '#FFFFFF',
  },
  secondary: {
    container: 'bg-white border border-gray-400 hover:bg-gray-100 text-black rounded px-4 py-2',
    label: 'text-black',
    spinnerColor: '#0A0A0A',
  },
  tertiary: {
    container: 'bg-gray-300 hover:bg-gray-400 text-gray-700 rounded px-4 py-2',
    label: 'text-gray-700',
    spinnerColor: '#525252',
  },
  outline: {
    container:
      'bg-transparent border border-gray-400 hover:bg-gray-100 text-black rounded-full px-4 py-2',
    label: 'text-black',
    spinnerColor: '#000000',
  },
  primaryOutline: {
    container:
      'bg-transparent border border-green-700 hover:bg-green-50 text-green-700 rounded-full px-4 py-2',
    label: 'text-green-700',
    spinnerColor: '#448600',
  },
} as const;
