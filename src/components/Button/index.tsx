/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { twMerge } from 'tailwind-merge';
import { VARIANTS } from './consts';
import { ButtonProps } from './types';

const Button = ({
  onPress,
  variant = 'primary',
  label,
  disabled,
  loading,
  className,
  labelClassName,
  prefixIcon,
  suffixIcon,
}: ButtonProps) => {
  const appliedVariant = disabled ? 'tertiary' : variant;
  const variantStyles = VARIANTS[appliedVariant];

  return (
    <Pressable
      onPress={onPress}
      disabled={disabled || loading}
      className={twMerge(
        'w-full p-3 rounded-lg justify-center items-center flex-row gap-2',
        variantStyles?.container,
        className,
      )}
    >
      {loading ? (
        <ActivityIndicator size="small" color={VARIANTS[variant].spinnerColor} />
      ) : (
        <View className="flex-row items-center gap-2">
          {prefixIcon}
          <Text
            className={twMerge(
              'text-base font-medium font-inter-medium',
              variantStyles?.label,
              labelClassName,
            )}
          >
            {label}
          </Text>
          {suffixIcon}
        </View>
      )}
    </Pressable>
  );
};

export default Button;
