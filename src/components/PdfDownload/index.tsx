import { Pressable, Text, View } from 'react-native';
import Pdf from '@/src/assets/svgs/Pdf';
import { PdfDownloadProps } from './types';

const PdfDownload = ({ onDownload }: PdfDownloadProps) => {
  return (
    <Pressable className="flex-row items-center gap-1" onPress={onDownload}>
      <Pdf />
      <View className="border border-[#448600] p-1 rounded-lg">
        <Text className="text-base">Download</Text>
      </View>
    </Pressable>
  );
};

export default PdfDownload;
