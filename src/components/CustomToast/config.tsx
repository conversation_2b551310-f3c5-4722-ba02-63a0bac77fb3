/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { ToastConfig, ToastConfigParams, ToastData } from 'react-native-toast-message';
import CustomToast from '.';

export const toastConfig: ToastConfig = {
  success: ({ text1, text2 }: ToastConfigParams<ToastData>) => (
    <CustomToast text={text1 || 'Success'} description={text2 || ''} variant="success" />
  ),
  error: ({ text1, text2 }: ToastConfigParams<ToastData>) => (
    <CustomToast text={text1 || 'Error'} description={text2 || ''} variant="error" />
  ),
  info: ({ text1, text2 }: ToastConfigParams<ToastData>) => (
    <CustomToast text={text1 || 'Note'} description={text2 || ''} variant="info" />
  ),
};
