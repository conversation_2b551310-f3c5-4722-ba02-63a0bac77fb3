/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Image, View, Text } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import { twMerge } from 'tailwind-merge';
import { UserAvatarProps } from './types';

const UserAvatar = ({
  avatarUri,
  name,
  width,
  height,
  className,
  placeholderClassName,
}: UserAvatarProps) => {
  const defaultSize = RFPercentage(5);
  const avatarWidth = width ?? defaultSize;
  const avatarHeight = height ?? defaultSize;

  const initial = name ? name.charAt(0).toUpperCase() : '';

  return (
    <View className={className}>
      {avatarUri ? (
        <Image
          source={{ uri: avatarUri }}
          style={{
            width: avatarWidth,
            height: avatarHeight,
            borderRadius: Math.max(avatarWidth, avatarHeight) / 2,
          }}
          className="rounded-full border-2 border-[#DDEFC8]"
        />
      ) : (
        <View
          className={twMerge(
            'justify-center items-center bg-[#006400] rounded-full',
            placeholderClassName,
          )}
          style={{
            width: avatarWidth,
            height: avatarHeight,
          }}
        >
          <Text
            className="text-white font-bold"
            style={{
              fontSize: Math.min(avatarWidth, avatarHeight) * 0.5,
            }}
          >
            {initial}
          </Text>
        </View>
      )}
    </View>
  );
};

export default UserAvatar;
