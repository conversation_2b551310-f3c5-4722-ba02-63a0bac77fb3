import { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import ChevronDown from '@/src/assets/svgs/ChevronDown';
import ChevronUp from '@/src/assets/svgs/ChevronUp';
import { AccordionPropsI } from './types';

const Accordion = ({
  title,
  subTitle,
  content,
  isLast,
  toggleExpand,
  defaultExpanded = false,
}: AccordionPropsI) => {
  const [expanded, setExpanded] = useState(defaultExpanded);
  const onToggle = () => {
    toggleExpand?.();
    setExpanded((prev) => !prev);
  };

  return (
    <View className="flex-col mt-6">
      <Pressable
        className="flex-row justify-between items-center"
        onPress={toggleExpand ? onToggle : () => setExpanded((prev) => !prev)}
      >
        <View className="flex gap-2">
          <Text className="font-semibold text-base leading-5">{title}</Text>
          {subTitle && <Text className="leading-4 text-sm">{subTitle}</Text>}
        </View>
        {expanded ? <ChevronUp /> : <ChevronDown />}
      </Pressable>
      {expanded && <View className="mt-3">{content}</View>}
      {!isLast && <View className="h-px bg-borderGrayLight w-full mt-6" />}
    </View>
  );
};

export default Accordion;
