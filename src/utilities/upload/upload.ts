/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { MediaI } from '@/src/screens/CreateContent/components/CreatePostForm/types';
import { showToast } from '../toast';

export const uploadFileWithPresignedUrl = async (file: MediaI, uploadUrl: string) => {
  try {
    const response = await fetch(file.uri);
    const blob = await response.blob();
    const uploadResponse = await fetch(uploadUrl, {
      method: 'PUT',
      body: blob,
      headers: {
        'Content-Type': file.type,
        'x-amz-acl': 'public-read',
      },
    });

    if (!uploadResponse.ok) {
      showToast({
        type: 'error',
        message: 'Upload Failed',
        description: 'Please try again or check your internet connection.',
      });
      throw new Error(`Upload failed with status: ${uploadResponse.status}`);
    }

    return true;
  } catch (error) {
    throw error;
  }
};
